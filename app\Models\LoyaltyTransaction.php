<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class LoyaltyTransaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'customer_id',
        'order_id',
        'type',
        'points',
        'description',
        'expiry_date',
    ];

    protected $casts = [
        'points' => 'integer',
        'expiry_date' => 'date',
    ];

    // Relationships
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    // Scopes
    public function scopeEarned($query)
    {
        return $query->where('type', 'earned');
    }

    public function scopeRedeemed($query)
    {
        return $query->where('type', 'redeemed');
    }

    public function scopeExpired($query)
    {
        return $query->where('type', 'expired');
    }

    public function scopeAdjusted($query)
    {
        return $query->where('type', 'adjusted');
    }

    public function scopeActive($query)
    {
        return $query->where('expiry_date', '>', now())
                    ->orWhereNull('expiry_date');
    }

    public function scopeExpiring($query, int $days = 30)
    {
        return $query->where('expiry_date', '<=', now()->addDays($days))
                    ->where('expiry_date', '>', now());
    }

    // Methods
    public function isExpired(): bool
    {
        return $this->expiry_date && $this->expiry_date < now();
    }

    public function isExpiringSoon(int $days = 30): bool
    {
        return $this->expiry_date && 
               $this->expiry_date <= now()->addDays($days) && 
               $this->expiry_date > now();
    }

    public function getTypeDisplayAttribute(): string
    {
        return match($this->type) {
            'earned' => 'Points Earned',
            'redeemed' => 'Points Redeemed',
            'expired' => 'Points Expired',
            'adjusted' => 'Points Adjusted',
            default => ucfirst($this->type)
        };
    }

    public function getTypeColorAttribute(): string
    {
        return match($this->type) {
            'earned' => 'green',
            'redeemed' => 'blue',
            'expired' => 'red',
            'adjusted' => 'yellow',
            default => 'gray'
        };
    }
}
