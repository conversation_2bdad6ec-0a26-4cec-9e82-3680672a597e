<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Reservation extends Model
{
    use HasFactory;

    protected $fillable = [
        'branch_id',
        'table_id',
        'customer_id',
        'customer_name',
        'customer_phone',
        'customer_email',
        'reservation_datetime',
        'party_size',
        'special_requests',
        'status',
        'cancelled_at',
        'cancellation_reason',
    ];

    protected $casts = [
        'reservation_datetime' => 'datetime',
        'party_size' => 'integer',
        'cancelled_at' => 'datetime',
    ];

    // Relationships
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    public function table(): BelongsTo
    {
        return $this->belongsTo(Table::class);
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    // Scopes
    public function scopeConfirmed($query)
    {
        return $query->where('status', 'confirmed');
    }

    public function scopeSeated($query)
    {
        return $query->where('status', 'seated');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeCancelled($query)
    {
        return $query->where('status', 'cancelled');
    }

    public function scopeNoShow($query)
    {
        return $query->where('status', 'no_show');
    }

    public function scopeToday($query)
    {
        return $query->whereDate('reservation_datetime', today());
    }

    public function scopeUpcoming($query)
    {
        return $query->where('reservation_datetime', '>=', now());
    }

    public function scopePast($query)
    {
        return $query->where('reservation_datetime', '<', now());
    }

    // Methods
    public function updateStatus(string $status, string $reason = null): bool
    {
        $validStatuses = ['confirmed', 'seated', 'completed', 'cancelled', 'no_show'];
        
        if (!in_array($status, $validStatuses)) {
            return false;
        }

        $data = ['status' => $status];

        if ($status === 'cancelled') {
            $data['cancelled_at'] = now();
            $data['cancellation_reason'] = $reason;
            
            // Free up the table
            $this->table->updateStatus('available');
        } elseif ($status === 'seated') {
            // Mark table as occupied
            $this->table->updateStatus('occupied');
        } elseif ($status === 'completed') {
            // Free up the table
            $this->table->updateStatus('available');
        }

        return $this->update($data);
    }

    public function cancel(string $reason = null): bool
    {
        if (!$this->canBeCancelled()) {
            return false;
        }

        return $this->updateStatus('cancelled', $reason);
    }

    public function canBeCancelled(): bool
    {
        return in_array($this->status, ['confirmed']) && $this->reservation_datetime > now();
    }

    public function markAsSeated(): bool
    {
        if ($this->status !== 'confirmed') {
            return false;
        }

        return $this->updateStatus('seated');
    }

    public function markAsCompleted(): bool
    {
        if ($this->status !== 'seated') {
            return false;
        }

        return $this->updateStatus('completed');
    }

    public function markAsNoShow(): bool
    {
        if ($this->status !== 'confirmed' || $this->reservation_datetime > now()) {
            return false;
        }

        return $this->updateStatus('no_show');
    }

    public function isToday(): bool
    {
        return $this->reservation_datetime->isToday();
    }

    public function isPast(): bool
    {
        return $this->reservation_datetime->isPast();
    }

    public function isFuture(): bool
    {
        return $this->reservation_datetime->isFuture();
    }

    public function getTimeUntilReservationAttribute(): string
    {
        if ($this->isPast()) {
            return 'Past';
        }

        return $this->reservation_datetime->diffForHumans();
    }

    public function getCustomerDisplayNameAttribute(): string
    {
        return $this->customer ? $this->customer->name : $this->customer_name;
    }

    public function getCustomerContactAttribute(): string
    {
        if ($this->customer) {
            return $this->customer->phone ?? $this->customer->email ?? '';
        }

        return $this->customer_phone ?? $this->customer_email ?? '';
    }
}
