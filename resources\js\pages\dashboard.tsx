import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { PageProps } from '@/types';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
    TrendingUp,
    TrendingDown,
    Users,
    ShoppingCart,
    DollarSign,
    Table,
    Clock,
    AlertTriangle,
    Calendar,
    ChefHat
} from 'lucide-react';

interface DashboardStats {
    orders: { today: number; change: number };
    revenue: { today: number; change: number };
    customers: { today: number; change: number };
    tables: { active: number; total: number; percentage: number };
}

interface Order {
    id: number;
    order_number: string;
    status: string;
    total_amount: number;
    created_at: string;
    table?: { name: string };
    customer?: { name: string };
    waiter?: { name: string };
}

interface Reservation {
    id: number;
    customer_display_name: string;
    reservation_datetime: string;
    party_size: number;
    status: string;
    table: { name: string };
}

interface InventoryItem {
    id: number;
    name: string;
    current_stock: number;
    minimum_stock: number;
    unit: string;
}

interface SalesData {
    date: string;
    revenue: number;
}

interface DashboardProps extends PageProps {
    stats: DashboardStats;
    recentOrders: Order[];
    todayReservations: Reservation[];
    tableStatus: Record<string, number>;
    lowStockItems: InventoryItem[];
    salesChartData: SalesData[];
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
];

export default function Dashboard({
    auth,
    stats,
    recentOrders,
    todayReservations,
    tableStatus,
    lowStockItems,
    salesChartData
}: DashboardProps) {
    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(amount);
    };

    const formatTime = (datetime: string) => {
        return new Date(datetime).toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const getStatusColor = (status: string) => {
        const colors: Record<string, string> = {
            pending: 'bg-yellow-100 text-yellow-800',
            confirmed: 'bg-blue-100 text-blue-800',
            preparing: 'bg-orange-100 text-orange-800',
            ready: 'bg-green-100 text-green-800',
            served: 'bg-purple-100 text-purple-800',
            completed: 'bg-gray-100 text-gray-800',
            cancelled: 'bg-red-100 text-red-800',
        };
        return colors[status] || 'bg-gray-100 text-gray-800';
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Dashboard" />

            <div className="flex h-full flex-1 flex-col gap-6 rounded-xl p-6">
                {/* Quick Actions */}
                <div className="flex justify-between items-center">
                    <h1 className="text-2xl font-bold">Restaurant Dashboard</h1>
                    <div className="flex space-x-2">
                        <Link href={route('orders.pos')}>
                            <Button>
                                <ShoppingCart className="w-4 h-4 mr-2" />
                                POS
                            </Button>
                        </Link>
                        <Link href={route('orders.kitchen')}>
                            <Button variant="outline">
                                <ChefHat className="w-4 h-4 mr-2" />
                                Kitchen
                            </Button>
                        </Link>
                    </div>
                </div>

                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Today's Orders</CardTitle>
                            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats?.orders?.today || 0}</div>
                            <div className="flex items-center text-xs text-muted-foreground">
                                {(stats?.orders?.change || 0) >= 0 ? (
                                    <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
                                ) : (
                                    <TrendingDown className="h-3 w-3 mr-1 text-red-500" />
                                )}
                                {Math.abs(stats?.orders?.change || 0).toFixed(1)}% from yesterday
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Today's Revenue</CardTitle>
                            <DollarSign className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatCurrency(stats?.revenue?.today || 0)}</div>
                            <div className="flex items-center text-xs text-muted-foreground">
                                {(stats?.revenue?.change || 0) >= 0 ? (
                                    <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
                                ) : (
                                    <TrendingDown className="h-3 w-3 mr-1 text-red-500" />
                                )}
                                {Math.abs(stats?.revenue?.change || 0).toFixed(1)}% from yesterday
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Today's Customers</CardTitle>
                            <Users className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats?.customers?.today || 0}</div>
                            <div className="flex items-center text-xs text-muted-foreground">
                                {(stats?.customers?.change || 0) >= 0 ? (
                                    <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
                                ) : (
                                    <TrendingDown className="h-3 w-3 mr-1 text-red-500" />
                                )}
                                {Math.abs(stats?.customers?.change || 0).toFixed(1)}% from yesterday
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Table Occupancy</CardTitle>
                            <Table className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">
                                {stats?.tables?.active || 0}/{stats?.tables?.total || 0}
                            </div>
                            <div className="text-xs text-muted-foreground">
                                {(stats?.tables?.percentage || 0).toFixed(1)}% occupied
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Main Content Grid */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Recent Orders */}
                    <div className="lg:col-span-2">
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between">
                                <CardTitle>Recent Orders</CardTitle>
                                <Link href={route('orders.index')}>
                                    <Button variant="outline" size="sm">View All</Button>
                                </Link>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    {recentOrders?.length > 0 ? recentOrders.map((order) => (
                                        <div key={order.id} className="flex items-center justify-between p-3 border rounded-lg">
                                            <div className="flex items-center space-x-3">
                                                <div>
                                                    <p className="font-medium">{order.order_number}</p>
                                                    <p className="text-sm text-muted-foreground">
                                                        {order.table?.name && `Table ${order.table.name} • `}
                                                        {order.customer?.name || 'Walk-in'}
                                                    </p>
                                                </div>
                                            </div>
                                            <div className="text-right">
                                                <p className="font-medium">{formatCurrency(order.total_amount)}</p>
                                                <Badge className={getStatusColor(order.status)}>
                                                    {order.status}
                                                </Badge>
                                            </div>
                                        </div>
                                    )) : (
                                        <p className="text-center text-muted-foreground py-8">No recent orders</p>
                                    )}
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Today's Reservations */}
                    <div>
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between">
                                <CardTitle>Today's Reservations</CardTitle>
                                <Link href={route('reservations.index')}>
                                    <Button variant="outline" size="sm">View All</Button>
                                </Link>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-3">
                                    {todayReservations?.length > 0 ? todayReservations.map((reservation) => (
                                        <div key={reservation.id} className="flex items-center justify-between p-3 border rounded-lg">
                                            <div>
                                                <p className="font-medium">{reservation.customer_display_name}</p>
                                                <p className="text-sm text-muted-foreground">
                                                    {formatTime(reservation.reservation_datetime)} • Table {reservation.table.name}
                                                </p>
                                                <p className="text-xs text-muted-foreground">
                                                    {reservation.party_size} guests
                                                </p>
                                            </div>
                                            <Badge className={getStatusColor(reservation.status)}>
                                                {reservation.status}
                                            </Badge>
                                        </div>
                                    )) : (
                                        <p className="text-center text-muted-foreground py-8">No reservations today</p>
                                    )}
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>

                {/* Bottom Row */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Table Status */}
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between">
                            <CardTitle>Table Status</CardTitle>
                            <Link href={route('tables.floor-plan')}>
                                <Button variant="outline" size="sm">Floor Plan</Button>
                            </Link>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-2 gap-4">
                                <div className="text-center p-4 border rounded-lg">
                                    <div className="text-2xl font-bold text-green-600">{tableStatus?.available || 0}</div>
                                    <div className="text-sm text-muted-foreground">Available</div>
                                </div>
                                <div className="text-center p-4 border rounded-lg">
                                    <div className="text-2xl font-bold text-red-600">{tableStatus?.occupied || 0}</div>
                                    <div className="text-sm text-muted-foreground">Occupied</div>
                                </div>
                                <div className="text-center p-4 border rounded-lg">
                                    <div className="text-2xl font-bold text-blue-600">{tableStatus?.reserved || 0}</div>
                                    <div className="text-sm text-muted-foreground">Reserved</div>
                                </div>
                                <div className="text-center p-4 border rounded-lg">
                                    <div className="text-2xl font-bold text-yellow-600">{tableStatus?.maintenance || 0}</div>
                                    <div className="text-sm text-muted-foreground">Maintenance</div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Low Stock Alert */}
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between">
                            <CardTitle className="flex items-center">
                                <AlertTriangle className="w-4 h-4 mr-2 text-orange-500" />
                                Low Stock Items
                            </CardTitle>
                            <Link href={route('inventory.index')}>
                                <Button variant="outline" size="sm">View Inventory</Button>
                            </Link>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                {lowStockItems?.length > 0 ? lowStockItems.map((item) => (
                                    <div key={item.id} className="flex items-center justify-between p-3 border rounded-lg border-orange-200">
                                        <div>
                                            <p className="font-medium">{item.name}</p>
                                            <p className="text-sm text-muted-foreground">
                                                Current: {item.current_stock} {item.unit}
                                            </p>
                                        </div>
                                        <Badge variant="destructive">
                                            Low Stock
                                        </Badge>
                                    </div>
                                )) : (
                                    <p className="text-center text-muted-foreground py-8">All items in stock</p>
                                )}
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
