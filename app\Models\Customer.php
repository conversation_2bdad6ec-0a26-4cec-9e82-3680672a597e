<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Customer extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'email',
        'phone',
        'date_of_birth',
        'address',
        'preferences',
        'loyalty_points',
        'last_visit',
        'is_active',
    ];

    protected $casts = [
        'date_of_birth' => 'date',
        'preferences' => 'array',
        'loyalty_points' => 'integer',
        'last_visit' => 'datetime',
        'is_active' => 'boolean',
    ];

    // Relationships
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    public function reservations(): HasMany
    {
        return $this->hasMany(Reservation::class);
    }

    public function invoices(): HasMany
    {
        return $this->hasMany(Invoice::class);
    }

    public function loyaltyTransactions(): HasMany
    {
        return $this->hasMany(LoyaltyTransaction::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeWithLoyaltyPoints($query, $minPoints = 0)
    {
        return $query->where('loyalty_points', '>=', $minPoints);
    }

    // Methods
    public function addLoyaltyPoints(int $points, ?Order $order = null, string $description = null): LoyaltyTransaction
    {
        $this->increment('loyalty_points', $points);

        return $this->loyaltyTransactions()->create([
            'order_id' => $order?->id,
            'type' => 'earned',
            'points' => $points,
            'description' => $description ?? "Points earned from order",
            'expiry_date' => now()->addYear(),
        ]);
    }

    public function redeemLoyaltyPoints(int $points, ?Order $order = null, string $description = null): bool
    {
        if ($this->loyalty_points < $points) {
            return false;
        }

        $this->decrement('loyalty_points', $points);

        $this->loyaltyTransactions()->create([
            'order_id' => $order?->id,
            'type' => 'redeemed',
            'points' => -$points,
            'description' => $description ?? "Points redeemed",
        ]);

        return true;
    }

    public function updateLastVisit(): void
    {
        $this->update(['last_visit' => now()]);
    }

    public function getTotalSpentAttribute(): float
    {
        return $this->orders()->where('status', 'completed')->sum('total_amount');
    }

    public function getOrderCountAttribute(): int
    {
        return $this->orders()->where('status', 'completed')->count();
    }
}
