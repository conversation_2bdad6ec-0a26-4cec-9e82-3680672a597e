<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class StaffShift extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'branch_id',
        'shift_date',
        'start_time',
        'end_time',
        'actual_start_time',
        'actual_end_time',
        'status',
        'notes',
    ];

    protected $casts = [
        'shift_date' => 'date',
        'start_time' => 'datetime:H:i',
        'end_time' => 'datetime:H:i',
        'actual_start_time' => 'datetime:H:i',
        'actual_end_time' => 'datetime:H:i',
    ];

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    // Scopes
    public function scopeScheduled($query)
    {
        return $query->where('status', 'scheduled');
    }

    public function scopeStarted($query)
    {
        return $query->where('status', 'started');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeCancelled($query)
    {
        return $query->where('status', 'cancelled');
    }

    public function scopeToday($query)
    {
        return $query->whereDate('shift_date', today());
    }

    public function scopeThisWeek($query)
    {
        return $query->whereBetween('shift_date', [now()->startOfWeek(), now()->endOfWeek()]);
    }

    public function scopeUpcoming($query)
    {
        return $query->where('shift_date', '>=', today());
    }

    // Methods
    public function startShift(): bool
    {
        if ($this->status !== 'scheduled') {
            return false;
        }

        return $this->update([
            'status' => 'started',
            'actual_start_time' => now()->format('H:i'),
        ]);
    }

    public function endShift(): bool
    {
        if ($this->status !== 'started') {
            return false;
        }

        return $this->update([
            'status' => 'completed',
            'actual_end_time' => now()->format('H:i'),
        ]);
    }

    public function cancel(string $reason = null): bool
    {
        if (!in_array($this->status, ['scheduled', 'started'])) {
            return false;
        }

        $notes = $this->notes;
        if ($reason) {
            $notes .= "\nCancellation reason: " . $reason;
        }

        return $this->update([
            'status' => 'cancelled',
            'notes' => $notes,
        ]);
    }

    public function getScheduledHoursAttribute(): float
    {
        if (!$this->start_time || !$this->end_time) {
            return 0;
        }

        $start = \Carbon\Carbon::createFromFormat('H:i', $this->start_time);
        $end = \Carbon\Carbon::createFromFormat('H:i', $this->end_time);

        if ($end->lt($start)) {
            $end->addDay(); // Handle overnight shifts
        }

        return $start->diffInHours($end, true);
    }

    public function getActualHoursAttribute(): float
    {
        if (!$this->actual_start_time || !$this->actual_end_time) {
            return 0;
        }

        $start = \Carbon\Carbon::createFromFormat('H:i', $this->actual_start_time);
        $end = \Carbon\Carbon::createFromFormat('H:i', $this->actual_end_time);

        if ($end->lt($start)) {
            $end->addDay(); // Handle overnight shifts
        }

        return $start->diffInHours($end, true);
    }

    public function getOvertimeHoursAttribute(): float
    {
        return max(0, $this->actual_hours - $this->scheduled_hours);
    }

    public function getEarningsAttribute(): float
    {
        if (!$this->user->hourly_rate) {
            return 0;
        }

        $regularHours = min($this->actual_hours, $this->scheduled_hours);
        $overtimeHours = $this->overtime_hours;
        $overtimeRate = $this->user->hourly_rate * 1.5; // 1.5x for overtime

        return ($regularHours * $this->user->hourly_rate) + ($overtimeHours * $overtimeRate);
    }

    public function isLate(): bool
    {
        if (!$this->actual_start_time || !$this->start_time) {
            return false;
        }

        $scheduled = \Carbon\Carbon::createFromFormat('H:i', $this->start_time);
        $actual = \Carbon\Carbon::createFromFormat('H:i', $this->actual_start_time);

        return $actual->gt($scheduled);
    }

    public function isEarly(): bool
    {
        if (!$this->actual_end_time || !$this->end_time) {
            return false;
        }

        $scheduled = \Carbon\Carbon::createFromFormat('H:i', $this->end_time);
        $actual = \Carbon\Carbon::createFromFormat('H:i', $this->actual_end_time);

        return $actual->lt($scheduled);
    }

    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'scheduled' => 'blue',
            'started' => 'yellow',
            'completed' => 'green',
            'cancelled' => 'red',
            default => 'gray'
        };
    }

    public function canBeStarted(): bool
    {
        return $this->status === 'scheduled' && $this->shift_date->isToday();
    }

    public function canBeEnded(): bool
    {
        return $this->status === 'started';
    }

    public function canBeCancelled(): bool
    {
        return in_array($this->status, ['scheduled', 'started']);
    }
}
