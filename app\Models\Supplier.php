<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Supplier extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'contact_person',
        'email',
        'phone',
        'address',
        'tax_number',
        'payment_terms',
        'is_active',
    ];

    protected $casts = [
        'payment_terms' => 'array',
        'is_active' => 'boolean',
    ];

    // Relationships
    public function purchaseOrders(): HasMany
    {
        return $this->hasMany(PurchaseOrder::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    // Methods
    public function getTotalOrdersAttribute(): int
    {
        return $this->purchaseOrders()->count();
    }

    public function getTotalOrderValueAttribute(): float
    {
        return $this->purchaseOrders()->sum('total_amount');
    }

    public function getLastOrderDateAttribute(): ?string
    {
        $lastOrder = $this->purchaseOrders()->latest()->first();
        return $lastOrder ? $lastOrder->created_at->format('Y-m-d') : null;
    }

    public function getPendingOrdersCountAttribute(): int
    {
        return $this->purchaseOrders()->whereIn('status', ['draft', 'sent', 'confirmed'])->count();
    }

    public function getPaymentTermsDisplayAttribute(): string
    {
        if (!$this->payment_terms) {
            return 'Not specified';
        }

        $terms = $this->payment_terms;
        $display = [];

        if (isset($terms['days'])) {
            $display[] = "Net {$terms['days']} days";
        }

        if (isset($terms['discount_percentage']) && isset($terms['discount_days'])) {
            $display[] = "{$terms['discount_percentage']}% discount if paid within {$terms['discount_days']} days";
        }

        return implode(', ', $display) ?: 'Standard terms';
    }

    public function canBeDeleted(): bool
    {
        return $this->purchaseOrders()->count() === 0;
    }

    public function deactivate(): bool
    {
        return $this->update(['is_active' => false]);
    }

    public function activate(): bool
    {
        return $this->update(['is_active' => true]);
    }
}
