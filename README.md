# Habibi Café Restaurant Management System

A comprehensive restaurant management system built with Laravel 12 and React/Inertia.js, featuring modern UI components and full restaurant operations management.

## Features

### 🏪 **Multi-Branch Support**
- Manage multiple restaurant locations
- Branch-specific data and reporting
- Centralized administration

### 🍽️ **Table Management**
- Interactive floor plan view
- Real-time table status tracking
- Reservation management with time slots
- Table capacity and positioning

### 📋 **Menu Management**
- Organized menu categories
- Item customizations and dietary information
- Dynamic pricing with tax calculation
- Availability tracking

### 🛒 **Order Management**
- Modern POS (Point of Sale) system
- Kitchen display for order preparation
- Order status tracking (pending → preparing → ready → served)
- Support for dine-in, takeaway, and delivery

### 👥 **Customer Management**
- Customer profiles with preferences and allergies
- Order history tracking
- Loyalty program with points system
- Customer search and management

### 📅 **Reservation System**
- Calendar view for reservations
- Availability checking
- Party size management
- Special requests handling

### 💰 **Payment & Billing**
- Multiple payment methods (cash, card, digital)
- Invoice generation with GST breakdown
- Discount and coupon management
- Bill splitting capabilities

### 👨‍💼 **Staff Management**
- Role-based access control (Manager, Waiter, Chef, Cashier)
- Employee scheduling and shift management
- Performance tracking
- Hourly rate and earnings calculation

### 📦 **Inventory Management**
- Stock level monitoring with low-stock alerts
- Supplier management
- Purchase order creation and tracking
- Automatic inventory reduction on orders

### 📊 **Reports & Analytics**
- Sales reports with category breakdown
- Tax compliance reporting
- Staff performance metrics
- Inventory level reports

### 🔔 **Notification System**
- Email/SMS notifications for orders and reservations
- System alerts for low stock
- Promotional messaging

## Technology Stack

- **Backend**: Laravel 12 (PHP 8.3+)
- **Frontend**: React 18 with TypeScript
- **UI Framework**: Inertia.js for seamless SPA experience
- **Styling**: Tailwind CSS with shadcn/ui components
- **Database**: MySQL/PostgreSQL
- **Icons**: Lucide React

## Installation

### Prerequisites
- PHP 8.3 or higher
- Composer
- Node.js 18+ and npm/yarn
- MySQL or PostgreSQL database

### Setup Instructions

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd habibi-cafe-restaurant
   ```

2. **Install PHP dependencies**
   ```bash
   composer install
   ```

3. **Install Node.js dependencies**
   ```bash
   npm install
   ```

4. **Environment setup**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

5. **Configure database**
   Edit `.env` file with your database credentials:
   ```env
   DB_CONNECTION=mysql
   DB_HOST=127.0.0.1
   DB_PORT=3306
   DB_DATABASE=habibi_cafe
   DB_USERNAME=your_username
   DB_PASSWORD=your_password
   ```

6. **Run database migrations and seeders**
   ```bash
   php artisan migrate --seed
   ```

7. **Build frontend assets**
   ```bash
   npm run build
   # or for development
   npm run dev
   ```

8. **Start the development server**
   ```bash
   php artisan serve
   ```

## Default Login Credentials

After running the seeders, you can log in with these accounts:

- **Manager**: <EMAIL> / password
- **Waiter**: <EMAIL> / password  
- **Chef**: <EMAIL> / password

## Key Features Overview

### Dashboard
- Real-time statistics (orders, revenue, customers, table occupancy)
- Recent orders and today's reservations
- Low stock alerts
- Quick access to POS and Kitchen views

### POS System
- Touch-friendly interface for order creation
- Menu browsing by category
- Real-time cart management
- Table selection for dine-in orders

### Kitchen Display
- Order queue management
- Item-level status tracking
- Priority indicators based on order time
- One-click status updates

### Floor Plan
- Visual table layout
- Drag-and-drop table positioning
- Real-time status indicators
- Quick table status changes

## API Endpoints

The system provides RESTful API endpoints for all major operations:

- `/api/tables` - Table management
- `/api/menu` - Menu and categories
- `/api/orders` - Order processing
- `/api/customers` - Customer management
- `/api/reservations` - Reservation system
- `/api/inventory` - Stock management
- `/api/staff` - Employee management

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

For support and questions, please contact:
- Email: <EMAIL>
- Documentation: [Link to docs]
- Issues: [GitHub Issues]
