import { Head, router } from '@inertiajs/react';
import { PageProps } from '@/types';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
    Clock,
    CheckCircle,
    AlertCircle,
    ChefHat,
    Table as TableIcon,
    Users,
    Timer
} from 'lucide-react';
import { useState, useEffect } from 'react';

interface OrderItem {
    id: number;
    quantity: number;
    status: string;
    special_instructions?: string;
    menu_item: {
        id: number;
        name: string;
        preparation_time?: number;
    };
}

interface Order {
    id: number;
    order_number: string;
    status: string;
    type: string;
    created_at: string;
    estimated_ready_time?: string;
    table?: { id: number; name: string };
    customer?: { id: number; name: string };
    order_items: OrderItem[];
}

interface KitchenProps extends PageProps {
    orders: Order[];
}

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Orders', href: '/orders' },
    { title: 'Kitchen', href: '/orders-kitchen' },
];

export default function Kitchen({ auth, orders }: KitchenProps) {
    const [currentTime, setCurrentTime] = useState(new Date());

    useEffect(() => {
        const timer = setInterval(() => {
            setCurrentTime(new Date());
        }, 1000);

        return () => clearInterval(timer);
    }, []);

    const formatTime = (datetime: string) => {
        return new Date(datetime).toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const getTimeSinceOrder = (datetime: string) => {
        const orderTime = new Date(datetime);
        const diffInMinutes = Math.floor((currentTime.getTime() - orderTime.getTime()) / (1000 * 60));
        
        if (diffInMinutes < 60) {
            return `${diffInMinutes}m ago`;
        } else {
            const hours = Math.floor(diffInMinutes / 60);
            const minutes = diffInMinutes % 60;
            return `${hours}h ${minutes}m ago`;
        }
    };

    const getOrderPriority = (datetime: string) => {
        const orderTime = new Date(datetime);
        const diffInMinutes = Math.floor((currentTime.getTime() - orderTime.getTime()) / (1000 * 60));
        
        if (diffInMinutes > 30) return 'high';
        if (diffInMinutes > 15) return 'medium';
        return 'low';
    };

    const getPriorityColor = (priority: string) => {
        switch (priority) {
            case 'high': return 'bg-red-100 text-red-800 border-red-200';
            case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
            case 'low': return 'bg-green-100 text-green-800 border-green-200';
            default: return 'bg-gray-100 text-gray-800 border-gray-200';
        }
    };

    const getStatusColor = (status: string) => {
        const colors: Record<string, string> = {
            pending: 'bg-yellow-100 text-yellow-800',
            preparing: 'bg-blue-100 text-blue-800',
            ready: 'bg-green-100 text-green-800',
            served: 'bg-gray-100 text-gray-800',
        };
        return colors[status] || 'bg-gray-100 text-gray-800';
    };

    const updateOrderStatus = (orderId: number, newStatus: string) => {
        router.patch(`/orders/${orderId}/status`, { status: newStatus }, {
            preserveScroll: true,
        });
    };

    const updateItemStatus = (itemId: number, newStatus: string) => {
        router.patch(`/order-items/${itemId}/status`, { status: newStatus }, {
            preserveScroll: true,
        });
    };

    const getTypeIcon = (type: string) => {
        switch (type) {
            case 'dine_in':
                return <TableIcon className="w-4 h-4" />;
            case 'takeaway':
                return <ChefHat className="w-4 h-4" />;
            case 'delivery':
                return <Users className="w-4 h-4" />;
            default:
                return <ChefHat className="w-4 h-4" />;
        }
    };

    // Group orders by status
    const confirmedOrders = orders.filter(order => order.status === 'confirmed');
    const preparingOrders = orders.filter(order => order.status === 'preparing');

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Kitchen Display" />
            
            <div className="flex h-full flex-1 flex-col gap-6 rounded-xl p-6">
                {/* Header */}
                <div className="flex justify-between items-center">
                    <div>
                        <h1 className="text-2xl font-bold">Kitchen Display</h1>
                        <p className="text-muted-foreground">
                            Current time: {currentTime.toLocaleTimeString('en-US')}
                        </p>
                    </div>
                    <div className="flex items-center space-x-4">
                        <Badge variant="outline" className="text-lg px-4 py-2">
                            <Timer className="w-4 h-4 mr-2" />
                            {orders.length} Active Orders
                        </Badge>
                    </div>
                </div>

                {/* Kitchen Board */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 h-full">
                    {/* New Orders */}
                    <div>
                        <h2 className="text-xl font-semibold mb-4 flex items-center">
                            <AlertCircle className="w-5 h-5 mr-2 text-orange-500" />
                            New Orders ({confirmedOrders.length})
                        </h2>
                        <div className="space-y-4 max-h-[calc(100vh-300px)] overflow-y-auto">
                            {confirmedOrders.map((order) => {
                                const priority = getOrderPriority(order.created_at);
                                return (
                                    <Card key={order.id} className={`border-2 ${getPriorityColor(priority)}`}>
                                        <CardHeader className="pb-3">
                                            <div className="flex justify-between items-start">
                                                <div>
                                                    <CardTitle className="text-lg">{order.order_number}</CardTitle>
                                                    <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                                                        {getTypeIcon(order.type)}
                                                        <span className="capitalize">{order.type.replace('_', ' ')}</span>
                                                        {order.table && (
                                                            <>
                                                                <span>•</span>
                                                                <span>Table {order.table.name}</span>
                                                            </>
                                                        )}
                                                    </div>
                                                </div>
                                                <div className="text-right">
                                                    <p className="text-sm font-medium">{getTimeSinceOrder(order.created_at)}</p>
                                                    <p className="text-xs text-muted-foreground">
                                                        {formatTime(order.created_at)}
                                                    </p>
                                                </div>
                                            </div>
                                        </CardHeader>
                                        <CardContent>
                                            <div className="space-y-3">
                                                {order.order_items.map((item) => (
                                                    <div key={item.id} className="flex justify-between items-center p-2 border rounded">
                                                        <div className="flex-1">
                                                            <p className="font-medium">
                                                                {item.quantity}x {item.menu_item.name}
                                                            </p>
                                                            {item.special_instructions && (
                                                                <p className="text-xs text-orange-600 mt-1">
                                                                    Note: {item.special_instructions}
                                                                </p>
                                                            )}
                                                            {item.menu_item.preparation_time && (
                                                                <p className="text-xs text-muted-foreground">
                                                                    Est. {item.menu_item.preparation_time}min
                                                                </p>
                                                            )}
                                                        </div>
                                                        <Badge className={getStatusColor(item.status)}>
                                                            {item.status}
                                                        </Badge>
                                                    </div>
                                                ))}
                                            </div>
                                            <div className="mt-4 flex space-x-2">
                                                <Button 
                                                    onClick={() => updateOrderStatus(order.id, 'preparing')}
                                                    className="flex-1"
                                                >
                                                    <ChefHat className="w-4 h-4 mr-2" />
                                                    Start Cooking
                                                </Button>
                                            </div>
                                        </CardContent>
                                    </Card>
                                );
                            })}
                            
                            {confirmedOrders.length === 0 && (
                                <Card>
                                    <CardContent className="text-center py-8">
                                        <CheckCircle className="w-12 h-12 mx-auto mb-2 text-green-500" />
                                        <p className="text-muted-foreground">No new orders</p>
                                    </CardContent>
                                </Card>
                            )}
                        </div>
                    </div>

                    {/* In Progress Orders */}
                    <div>
                        <h2 className="text-xl font-semibold mb-4 flex items-center">
                            <Clock className="w-5 h-5 mr-2 text-blue-500" />
                            In Progress ({preparingOrders.length})
                        </h2>
                        <div className="space-y-4 max-h-[calc(100vh-300px)] overflow-y-auto">
                            {preparingOrders.map((order) => {
                                const priority = getOrderPriority(order.created_at);
                                return (
                                    <Card key={order.id} className={`border-2 ${getPriorityColor(priority)}`}>
                                        <CardHeader className="pb-3">
                                            <div className="flex justify-between items-start">
                                                <div>
                                                    <CardTitle className="text-lg">{order.order_number}</CardTitle>
                                                    <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                                                        {getTypeIcon(order.type)}
                                                        <span className="capitalize">{order.type.replace('_', ' ')}</span>
                                                        {order.table && (
                                                            <>
                                                                <span>•</span>
                                                                <span>Table {order.table.name}</span>
                                                            </>
                                                        )}
                                                    </div>
                                                </div>
                                                <div className="text-right">
                                                    <p className="text-sm font-medium">{getTimeSinceOrder(order.created_at)}</p>
                                                    <p className="text-xs text-muted-foreground">
                                                        {formatTime(order.created_at)}
                                                    </p>
                                                </div>
                                            </div>
                                        </CardHeader>
                                        <CardContent>
                                            <div className="space-y-3">
                                                {order.order_items.map((item) => (
                                                    <div key={item.id} className="flex justify-between items-center p-2 border rounded">
                                                        <div className="flex-1">
                                                            <p className="font-medium">
                                                                {item.quantity}x {item.menu_item.name}
                                                            </p>
                                                            {item.special_instructions && (
                                                                <p className="text-xs text-orange-600 mt-1">
                                                                    Note: {item.special_instructions}
                                                                </p>
                                                            )}
                                                        </div>
                                                        <div className="flex items-center space-x-2">
                                                            <Badge className={getStatusColor(item.status)}>
                                                                {item.status}
                                                            </Badge>
                                                            {item.status === 'preparing' && (
                                                                <Button
                                                                    size="sm"
                                                                    onClick={() => updateItemStatus(item.id, 'ready')}
                                                                >
                                                                    Ready
                                                                </Button>
                                                            )}
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                            
                                            {order.order_items.every(item => item.status === 'ready') && (
                                                <div className="mt-4">
                                                    <Button 
                                                        onClick={() => updateOrderStatus(order.id, 'ready')}
                                                        className="w-full bg-green-600 hover:bg-green-700"
                                                    >
                                                        <CheckCircle className="w-4 h-4 mr-2" />
                                                        Order Complete - Ready for Service
                                                    </Button>
                                                </div>
                                            )}
                                        </CardContent>
                                    </Card>
                                );
                            })}
                            
                            {preparingOrders.length === 0 && (
                                <Card>
                                    <CardContent className="text-center py-8">
                                        <Clock className="w-12 h-12 mx-auto mb-2 text-blue-500" />
                                        <p className="text-muted-foreground">No orders in progress</p>
                                    </CardContent>
                                </Card>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
