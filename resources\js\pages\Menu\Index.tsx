import { Head, <PERSON> } from '@inertiajs/react';
import { PageProps } from '@/types';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
    Plus,
    Edit,
    Settings,
    Eye,
    EyeOff,
    DollarSign,
    Clock,
    Users
} from 'lucide-react';

interface MenuItem {
    id: number;
    name: string;
    description?: string;
    price: number;
    image?: string;
    dietary_info?: string[];
    is_taxable: boolean;
    customizations?: any[];
    preparation_time?: number;
    is_available: boolean;
    is_active: boolean;
    sort_order: number;
}

interface MenuCategory {
    id: number;
    name: string;
    description?: string;
    image?: string;
    sort_order: number;
    is_active: boolean;
    menu_items: MenuItem[];
}

interface MenuProps extends PageProps {
    categories: MenuCategory[];
}

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Menu', href: '/menu' },
];

export default function MenuIndex({ auth, categories }: MenuProps) {
    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-PK', {
            style: 'currency',
            currency: 'PKR'
        }).format(amount);
    };

    const getDietaryBadgeColor = (dietary: string) => {
        const colors: Record<string, string> = {
            vegan: 'bg-green-100 text-green-800',
            vegetarian: 'bg-green-100 text-green-800',
            'gluten-free': 'bg-blue-100 text-blue-800',
            'dairy-free': 'bg-purple-100 text-purple-800',
            spicy: 'bg-red-100 text-red-800',
        };
        return colors[dietary.toLowerCase()] || 'bg-gray-100 text-gray-800';
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Menu Management" />
            
            <div className="flex h-full flex-1 flex-col gap-6 rounded-xl p-6">
                {/* Header */}
                <div className="flex justify-between items-center">
                    <div>
                        <h1 className="text-2xl font-bold">Menu Management</h1>
                        <p className="text-muted-foreground">Manage your restaurant menu items and categories</p>
                    </div>
                    <div className="flex space-x-2">
                        <Link href={route('menu.categories')}>
                            <Button variant="outline">
                                <Settings className="w-4 h-4 mr-2" />
                                Manage Categories
                            </Button>
                        </Link>
                        <Link href={route('menu.items')}>
                            <Button variant="outline">
                                <Edit className="w-4 h-4 mr-2" />
                                Manage Items
                            </Button>
                        </Link>
                        <Link href={route('public.menu')} target="_blank">
                            <Button>
                                <Eye className="w-4 h-4 mr-2" />
                                View Public Menu
                            </Button>
                        </Link>
                    </div>
                </div>

                {/* Menu Categories */}
                <div className="space-y-8">
                    {categories.map((category) => (
                        <div key={category.id}>
                            <div className="flex justify-between items-center mb-4">
                                <div>
                                    <h2 className="text-xl font-semibold">{category.name}</h2>
                                    {category.description && (
                                        <p className="text-muted-foreground">{category.description}</p>
                                    )}
                                </div>
                                <Badge variant={category.is_active ? "default" : "secondary"}>
                                    {category.is_active ? 'Active' : 'Inactive'}
                                </Badge>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                {category.menu_items.map((item) => (
                                    <Card key={item.id} className={`relative ${!item.is_available ? 'opacity-60' : ''}`}>
                                        <CardHeader className="pb-3">
                                            <div className="flex justify-between items-start">
                                                <CardTitle className="text-lg">{item.name}</CardTitle>
                                                <div className="flex items-center space-x-1">
                                                    {!item.is_available && (
                                                        <EyeOff className="w-4 h-4 text-red-500" />
                                                    )}
                                                    {item.is_taxable && (
                                                        <Badge variant="outline" className="text-xs">Tax</Badge>
                                                    )}
                                                </div>
                                            </div>
                                            {item.description && (
                                                <p className="text-sm text-muted-foreground">{item.description}</p>
                                            )}
                                        </CardHeader>
                                        <CardContent>
                                            <div className="space-y-3">
                                                {/* Price */}
                                                <div className="flex items-center justify-between">
                                                    <span className="text-lg font-bold text-green-600">
                                                        {formatCurrency(item.price)}
                                                    </span>
                                                    {item.preparation_time && (
                                                        <div className="flex items-center text-sm text-muted-foreground">
                                                            <Clock className="w-3 h-3 mr-1" />
                                                            {item.preparation_time}min
                                                        </div>
                                                    )}
                                                </div>

                                                {/* Dietary Information */}
                                                {item.dietary_info && item.dietary_info.length > 0 && (
                                                    <div className="flex flex-wrap gap-1">
                                                        {item.dietary_info.map((dietary, index) => (
                                                            <Badge 
                                                                key={index} 
                                                                className={`text-xs ${getDietaryBadgeColor(dietary)}`}
                                                            >
                                                                {dietary}
                                                            </Badge>
                                                        ))}
                                                    </div>
                                                )}

                                                {/* Customizations */}
                                                {item.customizations && item.customizations.length > 0 && (
                                                    <div className="text-xs text-muted-foreground">
                                                        <span className="font-medium">Customizable:</span> 
                                                        {item.customizations.length} options available
                                                    </div>
                                                )}

                                                {/* Status */}
                                                <div className="flex justify-between items-center pt-2 border-t">
                                                    <Badge 
                                                        variant={item.is_available ? "default" : "secondary"}
                                                        className="text-xs"
                                                    >
                                                        {item.is_available ? 'Available' : 'Unavailable'}
                                                    </Badge>
                                                    <Badge 
                                                        variant={item.is_active ? "default" : "secondary"}
                                                        className="text-xs"
                                                    >
                                                        {item.is_active ? 'Active' : 'Inactive'}
                                                    </Badge>
                                                </div>
                                            </div>
                                        </CardContent>
                                    </Card>
                                ))}
                            </div>

                            {category.menu_items.length === 0 && (
                                <Card>
                                    <CardContent className="text-center py-8">
                                        <p className="text-muted-foreground">No menu items in this category</p>
                                        <Link href={route('menu.items')}>
                                            <Button className="mt-2">
                                                <Plus className="w-4 h-4 mr-2" />
                                                Add Menu Item
                                            </Button>
                                        </Link>
                                    </CardContent>
                                </Card>
                            )}
                        </div>
                    ))}
                </div>

                {categories.length === 0 && (
                    <Card>
                        <CardContent className="text-center py-12">
                            <h3 className="text-lg font-semibold mb-2">No Menu Categories</h3>
                            <p className="text-muted-foreground mb-4">
                                Start by creating your first menu category to organize your items.
                            </p>
                            <Link href={route('menu.categories')}>
                                <Button>
                                    <Plus className="w-4 h-4 mr-2" />
                                    Create Category
                                </Button>
                            </Link>
                        </CardContent>
                    </Card>
                )}
            </div>
        </AppLayout>
    );
}
