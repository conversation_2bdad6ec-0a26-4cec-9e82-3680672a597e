<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Table extends Model
{
    use HasFactory;

    protected $fillable = [
        'branch_id',
        'name',
        'seats',
        'status',
        'description',
        'position',
        'is_active',
    ];

    protected $casts = [
        'seats' => 'integer',
        'position' => 'array',
        'is_active' => 'boolean',
    ];

    // Relationships
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    public function reservations(): HasMany
    {
        return $this->hasMany(Reservation::class);
    }

    public function currentOrder(): HasMany
    {
        return $this->hasMany(Order::class)->whereIn('status', ['pending', 'confirmed', 'preparing', 'ready', 'served']);
    }

    public function currentReservation(): Has<PERSON>any
    {
        return $this->hasMany(Reservation::class)->where('status', 'confirmed')->where('reservation_datetime', '>=', now());
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeAvailable($query)
    {
        return $query->where('status', 'available');
    }

    public function scopeOccupied($query)
    {
        return $query->where('status', 'occupied');
    }

    public function scopeReserved($query)
    {
        return $query->where('status', 'reserved');
    }

    // Methods
    public function isAvailable(): bool
    {
        return $this->status === 'available' && $this->is_active;
    }

    public function canBeReserved(): bool
    {
        return in_array($this->status, ['available']) && $this->is_active;
    }

    public function updateStatus(string $status): bool
    {
        $validStatuses = ['available', 'occupied', 'reserved', 'maintenance'];
        
        if (!in_array($status, $validStatuses)) {
            return false;
        }

        return $this->update(['status' => $status]);
    }
}
