<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Invoice extends Model
{
    use HasFactory;

    protected $fillable = [
        'invoice_number',
        'order_id',
        'customer_id',
        'subtotal',
        'tax_amount',
        'discount_amount',
        'total_amount',
        'tax_breakdown',
        'discount_details',
        'status',
        'due_date',
        'paid_at',
    ];

    protected $casts = [
        'subtotal' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'tax_breakdown' => 'array',
        'discount_details' => 'array',
        'due_date' => 'datetime',
        'paid_at' => 'datetime',
    ];

    // Relationships
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    // Scopes
    public function scopeDraft($query)
    {
        return $query->where('status', 'draft');
    }

    public function scopeSent($query)
    {
        return $query->where('status', 'sent');
    }

    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }

    public function scopeOverdue($query)
    {
        return $query->where('status', 'overdue');
    }

    public function scopeCancelled($query)
    {
        return $query->where('status', 'cancelled');
    }

    // Methods
    public function markAsSent(): bool
    {
        if ($this->status !== 'draft') {
            return false;
        }

        return $this->update(['status' => 'sent']);
    }

    public function markAsPaid(): bool
    {
        if (!in_array($this->status, ['sent', 'overdue'])) {
            return false;
        }

        return $this->update([
            'status' => 'paid',
            'paid_at' => now(),
        ]);
    }

    public function markAsOverdue(): bool
    {
        if ($this->status !== 'sent' || !$this->due_date || $this->due_date->isFuture()) {
            return false;
        }

        return $this->update(['status' => 'overdue']);
    }

    public function cancel(): bool
    {
        if (!in_array($this->status, ['draft', 'sent'])) {
            return false;
        }

        return $this->update(['status' => 'cancelled']);
    }

    public function generateInvoiceNumber(): string
    {
        $prefix = 'INV';
        $date = now()->format('Ymd');
        $sequence = Invoice::whereDate('created_at', today())->count() + 1;
        
        return sprintf('%s-%s-%04d', $prefix, $date, $sequence);
    }

    public function isOverdue(): bool
    {
        return $this->status === 'sent' && $this->due_date && $this->due_date->isPast();
    }

    public function isPaid(): bool
    {
        return $this->status === 'paid';
    }

    public function getDaysOverdueAttribute(): int
    {
        if (!$this->isOverdue()) {
            return 0;
        }

        return $this->due_date->diffInDays(now());
    }

    public function getTaxBreakdownFormattedAttribute(): array
    {
        $breakdown = $this->tax_breakdown ?? [];
        $formatted = [];

        foreach ($breakdown as $taxType => $amount) {
            $formatted[] = [
                'type' => ucfirst(str_replace('_', ' ', $taxType)),
                'amount' => number_format($amount, 2),
            ];
        }

        return $formatted;
    }

    public function getDiscountDetailsFormattedAttribute(): array
    {
        $details = $this->discount_details ?? [];
        $formatted = [];

        foreach ($details as $discount) {
            $formatted[] = [
                'name' => $discount['name'] ?? 'Discount',
                'type' => $discount['type'] ?? 'fixed',
                'value' => $discount['value'] ?? 0,
                'amount' => number_format($discount['amount'] ?? 0, 2),
            ];
        }

        return $formatted;
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($invoice) {
            if (!$invoice->invoice_number) {
                $invoice->invoice_number = $invoice->generateInvoiceNumber();
            }
        });
    }
}
