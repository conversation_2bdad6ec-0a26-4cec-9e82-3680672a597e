<?php

namespace App\Http\Controllers;

use App\Models\Reservation;
use App\Models\Table;
use App\Models\Customer;
use App\Models\Branch;
use Illuminate\Http\Request;
use Inertia\Inertia;

class ReservationController extends Controller
{
    public function index(Request $request)
    {
        $user = $request->user();
        $branchId = $user->branch_id ?? Branch::first()?->id;

        $reservations = Reservation::with(['table', 'customer'])
            ->where('branch_id', $branchId)
            ->when($request->status, function($query, $status) {
                $query->where('status', $status);
            })
            ->when($request->date, function($query, $date) {
                $query->whereDate('reservation_datetime', $date);
            })
            ->when($request->search, function($query, $search) {
                $query->where('customer_name', 'like', "%{$search}%")
                     ->orWhere('customer_phone', 'like', "%{$search}%")
                     ->orWhereHas('customer', function($q) use ($search) {
                         $q->where('name', 'like', "%{$search}%");
                     });
            })
            ->orderBy('reservation_datetime')
            ->paginate(20);

        return Inertia::render('Reservations/Index', [
            'reservations' => $reservations,
            'filters' => $request->only(['status', 'date', 'search']),
        ]);
    }

    public function create(Request $request)
    {
        $user = $request->user();
        $branchId = $user->branch_id ?? Branch::first()?->id;

        $tables = Table::where('branch_id', $branchId)
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        $customers = Customer::active()
            ->orderBy('name')
            ->get();

        return Inertia::render('Reservations/Create', [
            'tables' => $tables,
            'customers' => $customers,
        ]);
    }

    public function store(Request $request)
    {
        $request->validate([
            'table_id' => 'required|exists:tables,id',
            'customer_id' => 'nullable|exists:customers,id',
            'customer_name' => 'required|string|max:255',
            'customer_phone' => 'nullable|string|max:20',
            'customer_email' => 'nullable|email',
            'reservation_datetime' => 'required|date|after:now',
            'party_size' => 'required|integer|min:1|max:20',
            'special_requests' => 'nullable|string',
        ]);

        $user = $request->user();
        $branchId = $user->branch_id ?? Branch::first()?->id;

        // Check if table is available at the requested time
        $table = Table::find($request->table_id);
        $conflictingReservation = Reservation::where('table_id', $request->table_id)
            ->where('status', 'confirmed')
            ->where('reservation_datetime', '>=', now()->parse($request->reservation_datetime)->subHours(2))
            ->where('reservation_datetime', '<=', now()->parse($request->reservation_datetime)->addHours(2))
            ->exists();

        if ($conflictingReservation) {
            return redirect()->back()->with('error', 'Table is not available at the requested time.');
        }

        if ($request->party_size > $table->seats) {
            return redirect()->back()->with('error', 'Party size exceeds table capacity.');
        }

        Reservation::create([
            'branch_id' => $branchId,
            'table_id' => $request->table_id,
            'customer_id' => $request->customer_id,
            'customer_name' => $request->customer_name,
            'customer_phone' => $request->customer_phone,
            'customer_email' => $request->customer_email,
            'reservation_datetime' => $request->reservation_datetime,
            'party_size' => $request->party_size,
            'special_requests' => $request->special_requests,
        ]);

        return redirect()->route('reservations.index')->with('success', 'Reservation created successfully.');
    }

    public function show(Reservation $reservation)
    {
        $reservation->load(['table', 'customer']);

        return Inertia::render('Reservations/Show', [
            'reservation' => $reservation,
        ]);
    }

    public function edit(Reservation $reservation)
    {
        $user = request()->user();
        $branchId = $user->branch_id ?? Branch::first()?->id;

        $tables = Table::where('branch_id', $branchId)
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        $customers = Customer::active()
            ->orderBy('name')
            ->get();

        return Inertia::render('Reservations/Edit', [
            'reservation' => $reservation,
            'tables' => $tables,
            'customers' => $customers,
        ]);
    }

    public function update(Request $request, Reservation $reservation)
    {
        $request->validate([
            'table_id' => 'required|exists:tables,id',
            'customer_id' => 'nullable|exists:customers,id',
            'customer_name' => 'required|string|max:255',
            'customer_phone' => 'nullable|string|max:20',
            'customer_email' => 'nullable|email',
            'reservation_datetime' => 'required|date',
            'party_size' => 'required|integer|min:1|max:20',
            'special_requests' => 'nullable|string',
        ]);

        if (!$reservation->canBeCancelled() && $reservation->status !== 'confirmed') {
            return redirect()->back()->with('error', 'Cannot modify this reservation.');
        }

        // Check if table is available at the new time (excluding current reservation)
        $table = Table::find($request->table_id);
        $conflictingReservation = Reservation::where('table_id', $request->table_id)
            ->where('id', '!=', $reservation->id)
            ->where('status', 'confirmed')
            ->where('reservation_datetime', '>=', now()->parse($request->reservation_datetime)->subHours(2))
            ->where('reservation_datetime', '<=', now()->parse($request->reservation_datetime)->addHours(2))
            ->exists();

        if ($conflictingReservation) {
            return redirect()->back()->with('error', 'Table is not available at the requested time.');
        }

        if ($request->party_size > $table->seats) {
            return redirect()->back()->with('error', 'Party size exceeds table capacity.');
        }

        $reservation->update($request->only([
            'table_id', 'customer_id', 'customer_name', 'customer_phone',
            'customer_email', 'reservation_datetime', 'party_size', 'special_requests'
        ]));

        return redirect()->route('reservations.show', $reservation)->with('success', 'Reservation updated successfully.');
    }

    public function updateStatus(Request $request, Reservation $reservation)
    {
        $request->validate([
            'status' => 'required|in:confirmed,seated,completed,cancelled,no_show',
            'reason' => 'nullable|string',
        ]);

        $reservation->updateStatus($request->status, $request->reason);

        return response()->json([
            'message' => 'Reservation status updated successfully.',
            'reservation' => $reservation->fresh(['table', 'customer']),
        ]);
    }

    public function cancel(Request $request, Reservation $reservation)
    {
        $request->validate([
            'reason' => 'nullable|string',
        ]);

        if (!$reservation->cancel($request->reason)) {
            return response()->json(['error' => 'Cannot cancel this reservation.'], 422);
        }

        return response()->json([
            'message' => 'Reservation cancelled successfully.',
            'reservation' => $reservation->fresh(['table', 'customer']),
        ]);
    }

    public function calendar(Request $request)
    {
        $user = $request->user();
        $branchId = $user->branch_id ?? Branch::first()?->id;

        $startDate = $request->start ?? now()->startOfMonth();
        $endDate = $request->end ?? now()->endOfMonth();

        $reservations = Reservation::with(['table', 'customer'])
            ->where('branch_id', $branchId)
            ->whereBetween('reservation_datetime', [$startDate, $endDate])
            ->get()
            ->map(function($reservation) {
                return [
                    'id' => $reservation->id,
                    'title' => $reservation->customer_display_name . ' - Table ' . $reservation->table->name,
                    'start' => $reservation->reservation_datetime->toISOString(),
                    'end' => $reservation->reservation_datetime->addHours(2)->toISOString(),
                    'color' => $reservation->status_color,
                    'extendedProps' => [
                        'reservation' => $reservation,
                    ],
                ];
            });

        return Inertia::render('Reservations/Calendar', [
            'reservations' => $reservations,
        ]);
    }

    public function checkAvailability(Request $request)
    {
        $request->validate([
            'table_id' => 'required|exists:tables,id',
            'datetime' => 'required|date',
            'party_size' => 'required|integer|min:1',
        ]);

        $table = Table::find($request->table_id);
        $datetime = now()->parse($request->datetime);

        // Check table capacity
        if ($request->party_size > $table->seats) {
            return response()->json([
                'available' => false,
                'reason' => 'Party size exceeds table capacity.',
            ]);
        }

        // Check for conflicting reservations
        $conflictingReservation = Reservation::where('table_id', $request->table_id)
            ->where('status', 'confirmed')
            ->where('reservation_datetime', '>=', $datetime->copy()->subHours(2))
            ->where('reservation_datetime', '<=', $datetime->copy()->addHours(2))
            ->exists();

        if ($conflictingReservation) {
            return response()->json([
                'available' => false,
                'reason' => 'Table is not available at the requested time.',
            ]);
        }

        return response()->json([
            'available' => true,
            'table' => $table,
        ]);
    }
}
