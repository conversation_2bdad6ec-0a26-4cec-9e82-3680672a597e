<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class StaffRole extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'permissions',
        'is_active',
    ];

    protected $casts = [
        'permissions' => 'array',
        'is_active' => 'boolean',
    ];

    // Relationships
    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    // Methods
    public function hasPermission(string $permission): bool
    {
        return in_array($permission, $this->permissions ?? []);
    }

    public function addPermission(string $permission): bool
    {
        $permissions = $this->permissions ?? [];
        
        if (!in_array($permission, $permissions)) {
            $permissions[] = $permission;
            return $this->update(['permissions' => $permissions]);
        }

        return true;
    }

    public function removePermission(string $permission): bool
    {
        $permissions = $this->permissions ?? [];
        $permissions = array_filter($permissions, fn($p) => $p !== $permission);
        
        return $this->update(['permissions' => array_values($permissions)]);
    }

    public function getUserCountAttribute(): int
    {
        return $this->users()->where('is_staff', true)->count();
    }

    public function getActiveUserCountAttribute(): int
    {
        return $this->users()->where('is_staff', true)->where('employment_status', 'active')->count();
    }

    public function canBeDeleted(): bool
    {
        return $this->users()->count() === 0;
    }

    // Default permissions for different roles
    public static function getDefaultPermissions(string $roleName): array
    {
        return match(strtolower($roleName)) {
            'manager' => [
                'view_dashboard',
                'manage_staff',
                'manage_menu',
                'manage_inventory',
                'view_reports',
                'manage_orders',
                'manage_tables',
                'manage_customers',
                'manage_reservations',
                'process_payments',
                'manage_discounts',
            ],
            'waiter' => [
                'view_dashboard',
                'manage_orders',
                'manage_tables',
                'view_customers',
                'manage_reservations',
                'process_payments',
            ],
            'chef' => [
                'view_dashboard',
                'view_orders',
                'update_order_status',
                'view_menu',
                'view_inventory',
            ],
            'cashier' => [
                'view_dashboard',
                'view_orders',
                'process_payments',
                'view_customers',
                'apply_discounts',
            ],
            default => ['view_dashboard']
        };
    }
}
