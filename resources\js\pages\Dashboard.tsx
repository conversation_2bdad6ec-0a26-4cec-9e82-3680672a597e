import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { PageProps } from '@/types';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
    ShoppingCart, 
    DollarSign, 
    Users, 
    Table,
    ChefHat,
    Calendar,
    Package
} from 'lucide-react';

interface DashboardProps extends PageProps {
    stats?: any;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
];

export default function Dashboard({ auth, stats }: DashboardProps) {
    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-PK', {
            style: 'currency',
            currency: 'PKR'
        }).format(amount);
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Dashboard" />
            
            <div className="flex h-full flex-1 flex-col gap-6 rounded-xl p-6">
                <div className="flex justify-between items-center">
                    <div>
                        <h1 className="text-3xl font-bold">Welcome to Habibi Café</h1>
                        <p className="text-muted-foreground">Restaurant Management System</p>
                    </div>
                    <div className="flex space-x-2">
                        <Link href="/orders-pos">
                            <Button size="lg">
                                <ShoppingCart className="w-4 h-4 mr-2" />
                                Start POS
                            </Button>
                        </Link>
                        <Link href="/orders-kitchen">
                            <Button variant="outline" size="lg">
                                <ChefHat className="w-4 h-4 mr-2" />
                                Kitchen
                            </Button>
                        </Link>
                    </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Today's Orders</CardTitle>
                            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats?.orders?.today || 0}</div>
                            <p className="text-xs text-muted-foreground">Orders processed today</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Today's Revenue</CardTitle>
                            <DollarSign className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatCurrency(stats?.revenue?.today || 0)}</div>
                            <p className="text-xs text-muted-foreground">Total sales today</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Customers</CardTitle>
                            <Users className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats?.customers?.today || 0}</div>
                            <p className="text-xs text-muted-foreground">Customers served today</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Tables</CardTitle>
                            <Table className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">
                                {stats?.tables?.active || 0}/{stats?.tables?.total || 8}
                            </div>
                            <p className="text-xs text-muted-foreground">Tables occupied</p>
                        </CardContent>
                    </Card>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center">
                                <ShoppingCart className="w-5 h-5 mr-2" />
                                Orders
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-2">
                            <Link href="/orders">
                                <Button variant="outline" className="w-full justify-start">
                                    View All Orders
                                </Button>
                            </Link>
                            <Link href="/orders/create">
                                <Button variant="outline" className="w-full justify-start">
                                    Create New Order
                                </Button>
                            </Link>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center">
                                <Table className="w-5 h-5 mr-2" />
                                Tables
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-2">
                            <Link href="/tables">
                                <Button variant="outline" className="w-full justify-start">
                                    Manage Tables
                                </Button>
                            </Link>
                            <Link href="/tables-floor-plan">
                                <Button variant="outline" className="w-full justify-start">
                                    Floor Plan
                                </Button>
                            </Link>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center">
                                <Users className="w-5 h-5 mr-2" />
                                Customers
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-2">
                            <Link href="/customers">
                                <Button variant="outline" className="w-full justify-start">
                                    View Customers
                                </Button>
                            </Link>
                            <Link href="/customers/create">
                                <Button variant="outline" className="w-full justify-start">
                                    Add Customer
                                </Button>
                            </Link>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center">
                                <Calendar className="w-5 h-5 mr-2" />
                                Reservations
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-2">
                            <Link href="/reservations">
                                <Button variant="outline" className="w-full justify-start">
                                    View Reservations
                                </Button>
                            </Link>
                            <Link href="/reservations/create">
                                <Button variant="outline" className="w-full justify-start">
                                    New Reservation
                                </Button>
                            </Link>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center">
                                <Package className="w-5 h-5 mr-2" />
                                Menu
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-2">
                            <Link href="/menu">
                                <Button variant="outline" className="w-full justify-start">
                                    View Menu
                                </Button>
                            </Link>
                            <Link href="/menu/items">
                                <Button variant="outline" className="w-full justify-start">
                                    Manage Items
                                </Button>
                            </Link>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="text-center py-12">
                            <h3 className="text-lg font-semibold mb-2">Quick Start</h3>
                            <p className="text-muted-foreground mb-4">
                                Get started with your restaurant management system
                            </p>
                            <div className="flex justify-center space-x-4">
                                <Link href="/orders-pos">
                                    <Button size="lg">
                                        <ShoppingCart className="w-4 h-4 mr-2" />
                                        Take Order
                                    </Button>
                                </Link>
                                <Link href="/menu">
                                    <Button variant="outline" size="lg">
                                        <Package className="w-4 h-4 mr-2" />
                                        View Menu
                                    </Button>
                                </Link>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
