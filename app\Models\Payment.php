<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Payment extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'payment_method',
        'amount',
        'status',
        'transaction_id',
        'payment_details',
        'processed_at',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'payment_details' => 'array',
        'processed_at' => 'datetime',
    ];

    // Relationships
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    public function scopeRefunded($query)
    {
        return $query->where('status', 'refunded');
    }

    public function scopeCash($query)
    {
        return $query->where('payment_method', 'cash');
    }

    public function scopeCard($query)
    {
        return $query->where('payment_method', 'card');
    }

    // Methods
    public function markAsCompleted(string $transactionId = null, array $details = []): bool
    {
        if ($this->status !== 'pending') {
            return false;
        }

        return $this->update([
            'status' => 'completed',
            'transaction_id' => $transactionId,
            'payment_details' => array_merge($this->payment_details ?? [], $details),
            'processed_at' => now(),
        ]);
    }

    public function markAsFailed(string $reason = null): bool
    {
        if ($this->status !== 'pending') {
            return false;
        }

        $details = $this->payment_details ?? [];
        if ($reason) {
            $details['failure_reason'] = $reason;
        }

        return $this->update([
            'status' => 'failed',
            'payment_details' => $details,
            'processed_at' => now(),
        ]);
    }

    public function refund(float $amount = null, string $reason = null): bool
    {
        if ($this->status !== 'completed') {
            return false;
        }

        $refundAmount = $amount ?? $this->amount;
        
        if ($refundAmount > $this->amount) {
            return false;
        }

        $details = $this->payment_details ?? [];
        $details['refund_amount'] = $refundAmount;
        $details['refund_reason'] = $reason;
        $details['refunded_at'] = now()->toISOString();

        return $this->update([
            'status' => 'refunded',
            'payment_details' => $details,
        ]);
    }

    public function isSuccessful(): bool
    {
        return $this->status === 'completed';
    }

    public function isFailed(): bool
    {
        return $this->status === 'failed';
    }

    public function isRefunded(): bool
    {
        return $this->status === 'refunded';
    }

    public function getRefundAmountAttribute(): float
    {
        if (!$this->isRefunded()) {
            return 0;
        }

        return $this->payment_details['refund_amount'] ?? 0;
    }

    public function getDisplayMethodAttribute(): string
    {
        return match($this->payment_method) {
            'cash' => 'Cash',
            'card' => 'Credit/Debit Card',
            'digital_wallet' => 'Digital Wallet',
            'bank_transfer' => 'Bank Transfer',
            'loyalty_points' => 'Loyalty Points',
            default => ucfirst(str_replace('_', ' ', $this->payment_method))
        };
    }
}
