<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class MenuItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'branch_id',
        'menu_category_id',
        'name',
        'description',
        'price',
        'image',
        'dietary_info',
        'is_taxable',
        'customizations',
        'preparation_time',
        'is_available',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'dietary_info' => 'array',
        'is_taxable' => 'boolean',
        'customizations' => 'array',
        'preparation_time' => 'integer',
        'is_available' => 'boolean',
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    // Relationships
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    public function menuCategory(): BelongsTo
    {
        return $this->belongsTo(MenuCategory::class);
    }

    public function orderItems(): HasMany
    {
        return $this->hasMany(OrderItem::class);
    }

    public function inventoryItems(): BelongsToMany
    {
        return $this->belongsToMany(InventoryItem::class, 'menu_item_ingredients')
            ->withPivot('quantity_required')
            ->withTimestamps();
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeAvailable($query)
    {
        return $query->where('is_available', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    public function scopeTaxable($query)
    {
        return $query->where('is_taxable', true);
    }

    // Methods
    public function calculateTaxAmount(float $taxRate): float
    {
        if (!$this->is_taxable) {
            return 0;
        }

        return round($this->price * ($taxRate / 100), 2);
    }

    public function getPriceWithTax(float $taxRate): float
    {
        return $this->price + $this->calculateTaxAmount($taxRate);
    }

    public function isInStock(): bool
    {
        if (!$this->inventoryItems()->exists()) {
            return true; // No inventory tracking for this item
        }

        foreach ($this->inventoryItems as $inventoryItem) {
            $requiredQuantity = $inventoryItem->pivot->quantity_required;
            if ($inventoryItem->current_stock < $requiredQuantity) {
                return false;
            }
        }

        return true;
    }

    public function canBePrepared(int $quantity = 1): bool
    {
        if (!$this->is_available || !$this->is_active) {
            return false;
        }

        if (!$this->inventoryItems()->exists()) {
            return true; // No inventory tracking
        }

        foreach ($this->inventoryItems as $inventoryItem) {
            $requiredQuantity = $inventoryItem->pivot->quantity_required * $quantity;
            if ($inventoryItem->current_stock < $requiredQuantity) {
                return false;
            }
        }

        return true;
    }

    public function reduceInventory(int $quantity = 1): void
    {
        foreach ($this->inventoryItems as $inventoryItem) {
            $requiredQuantity = $inventoryItem->pivot->quantity_required * $quantity;
            $inventoryItem->decrement('current_stock', $requiredQuantity);
            
            // Create inventory transaction
            InventoryTransaction::create([
                'inventory_item_id' => $inventoryItem->id,
                'type' => 'out',
                'quantity' => -$requiredQuantity,
                'reference_type' => 'menu_item',
                'reference_id' => $this->id,
                'notes' => "Used for menu item: {$this->name}",
                'created_by' => auth()->id() ?? 1,
            ]);
        }
    }
}
