<?php

namespace App\Http\Controllers;

use App\Models\MenuCategory;
use App\Models\MenuItem;
use App\Models\Branch;
use Illuminate\Http\Request;
use Inertia\Inertia;

class MenuController extends Controller
{
    public function index(Request $request)
    {
        $user = $request->user();
        $branchId = $user->branch_id ?? Branch::first()?->id;

        $categories = MenuCategory::with(['menuItems' => function($query) {
            $query->active()->ordered();
        }])
        ->where('branch_id', $branchId)
        ->active()
        ->ordered()
        ->get();

        return Inertia::render('Menu/Index', [
            'categories' => $categories,
        ]);
    }

    public function categories(Request $request)
    {
        $user = $request->user();
        $branchId = $user->branch_id ?? Branch::first()?->id;

        $categories = MenuCategory::where('branch_id', $branchId)
            ->withCount('menuItems')
            ->orderBy('sort_order')
            ->get();

        return Inertia::render('Menu/Categories', [
            'categories' => $categories,
        ]);
    }

    public function storeCategory(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'sort_order' => 'nullable|integer',
        ]);

        $user = $request->user();
        $branchId = $user->branch_id ?? Branch::first()?->id;

        MenuCategory::create([
            'branch_id' => $branchId,
            'name' => $request->name,
            'description' => $request->description,
            'sort_order' => $request->sort_order ?? 0,
        ]);

        return redirect()->back()->with('success', 'Category created successfully.');
    }

    public function updateCategory(Request $request, MenuCategory $category)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'sort_order' => 'nullable|integer',
            'is_active' => 'boolean',
        ]);

        $category->update($request->only([
            'name', 'description', 'sort_order', 'is_active'
        ]));

        return redirect()->back()->with('success', 'Category updated successfully.');
    }

    public function destroyCategory(MenuCategory $category)
    {
        if ($category->menuItems()->exists()) {
            return redirect()->back()->with('error', 'Cannot delete category with menu items.');
        }

        $category->delete();

        return redirect()->back()->with('success', 'Category deleted successfully.');
    }

    public function items(Request $request)
    {
        $user = $request->user();
        $branchId = $user->branch_id ?? Branch::first()?->id;

        $items = MenuItem::with(['menuCategory', 'inventoryItems'])
            ->where('branch_id', $branchId)
            ->when($request->category_id, function($query, $categoryId) {
                $query->where('menu_category_id', $categoryId);
            })
            ->when($request->search, function($query, $search) {
                $query->where('name', 'like', "%{$search}%");
            })
            ->orderBy('sort_order')
            ->paginate(20);

        $categories = MenuCategory::where('branch_id', $branchId)
            ->active()
            ->ordered()
            ->get();

        return Inertia::render('Menu/Items', [
            'items' => $items,
            'categories' => $categories,
            'filters' => $request->only(['category_id', 'search']),
        ]);
    }

    public function storeItem(Request $request)
    {
        $request->validate([
            'menu_category_id' => 'required|exists:menu_categories,id',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'dietary_info' => 'nullable|array',
            'is_taxable' => 'boolean',
            'customizations' => 'nullable|array',
            'preparation_time' => 'nullable|integer|min:0',
            'sort_order' => 'nullable|integer',
        ]);

        $user = $request->user();
        $branchId = $user->branch_id ?? Branch::first()?->id;

        MenuItem::create([
            'branch_id' => $branchId,
            'menu_category_id' => $request->menu_category_id,
            'name' => $request->name,
            'description' => $request->description,
            'price' => $request->price,
            'dietary_info' => $request->dietary_info,
            'is_taxable' => $request->boolean('is_taxable', true),
            'customizations' => $request->customizations,
            'preparation_time' => $request->preparation_time,
            'sort_order' => $request->sort_order ?? 0,
        ]);

        return redirect()->back()->with('success', 'Menu item created successfully.');
    }

    public function updateItem(Request $request, MenuItem $item)
    {
        $request->validate([
            'menu_category_id' => 'required|exists:menu_categories,id',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'dietary_info' => 'nullable|array',
            'is_taxable' => 'boolean',
            'customizations' => 'nullable|array',
            'preparation_time' => 'nullable|integer|min:0',
            'sort_order' => 'nullable|integer',
            'is_available' => 'boolean',
            'is_active' => 'boolean',
        ]);

        $item->update($request->only([
            'menu_category_id', 'name', 'description', 'price', 'dietary_info',
            'is_taxable', 'customizations', 'preparation_time', 'sort_order',
            'is_available', 'is_active'
        ]));

        return redirect()->back()->with('success', 'Menu item updated successfully.');
    }

    public function toggleAvailability(MenuItem $item)
    {
        $item->update(['is_available' => !$item->is_available]);

        return response()->json([
            'message' => 'Item availability updated successfully.',
            'item' => $item->fresh(),
        ]);
    }

    public function destroyItem(MenuItem $item)
    {
        if ($item->orderItems()->exists()) {
            return redirect()->back()->with('error', 'Cannot delete menu item with existing orders.');
        }

        $item->delete();

        return redirect()->back()->with('success', 'Menu item deleted successfully.');
    }

    public function publicMenu(Request $request)
    {
        $branchId = $request->branch_id ?? Branch::first()?->id;

        $categories = MenuCategory::with(['availableMenuItems'])
            ->where('branch_id', $branchId)
            ->active()
            ->ordered()
            ->get();

        return Inertia::render('PublicMenu', [
            'categories' => $categories,
        ]);
    }
}
