<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use App\Models\LoyaltyTransaction;
use Illuminate\Http\Request;
use Inertia\Inertia;

class CustomerController extends Controller
{
    public function index(Request $request)
    {
        $customers = Customer::when($request->search, function($query, $search) {
                $query->where('name', 'like', "%{$search}%")
                     ->orWhere('email', 'like', "%{$search}%")
                     ->orWhere('phone', 'like', "%{$search}%");
            })
            ->when($request->loyalty_points, function($query, $points) {
                $query->where('loyalty_points', '>=', $points);
            })
            ->withCount('orders')
            ->orderBy('name')
            ->paginate(20);

        return Inertia::render('Customers/Index', [
            'customers' => $customers,
            'filters' => $request->only(['search', 'loyalty_points']),
        ]);
    }

    public function create()
    {
        return Inertia::render('Customers/Create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'nullable|email|unique:customers,email',
            'phone' => 'nullable|string|max:20',
            'date_of_birth' => 'nullable|date',
            'address' => 'nullable|string',
            'preferences' => 'nullable|array',
        ]);

        Customer::create($request->only([
            'name', 'email', 'phone', 'date_of_birth', 'address', 'preferences'
        ]));

        return redirect()->route('customers.index')->with('success', 'Customer created successfully.');
    }

    public function show(Customer $customer)
    {
        $customer->load(['orders.orderItems.menuItem', 'loyaltyTransactions']);

        $recentOrders = $customer->orders()
            ->with(['orderItems.menuItem'])
            ->latest()
            ->take(10)
            ->get();

        $loyaltyHistory = $customer->loyaltyTransactions()
            ->latest()
            ->take(20)
            ->get();

        return Inertia::render('Customers/Show', [
            'customer' => $customer,
            'recentOrders' => $recentOrders,
            'loyaltyHistory' => $loyaltyHistory,
        ]);
    }

    public function edit(Customer $customer)
    {
        return Inertia::render('Customers/Edit', [
            'customer' => $customer,
        ]);
    }

    public function update(Request $request, Customer $customer)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'nullable|email|unique:customers,email,' . $customer->id,
            'phone' => 'nullable|string|max:20',
            'date_of_birth' => 'nullable|date',
            'address' => 'nullable|string',
            'preferences' => 'nullable|array',
            'is_active' => 'boolean',
        ]);

        $customer->update($request->only([
            'name', 'email', 'phone', 'date_of_birth', 'address', 'preferences', 'is_active'
        ]));

        return redirect()->route('customers.show', $customer)->with('success', 'Customer updated successfully.');
    }

    public function destroy(Customer $customer)
    {
        if ($customer->orders()->exists()) {
            return redirect()->back()->with('error', 'Cannot delete customer with existing orders.');
        }

        $customer->delete();

        return redirect()->route('customers.index')->with('success', 'Customer deleted successfully.');
    }

    public function addLoyaltyPoints(Request $request, Customer $customer)
    {
        $request->validate([
            'points' => 'required|integer|min:1',
            'description' => 'nullable|string',
        ]);

        $customer->addLoyaltyPoints(
            $request->points,
            null,
            $request->description ?? 'Manual points addition'
        );

        return response()->json([
            'message' => 'Loyalty points added successfully.',
            'customer' => $customer->fresh(),
        ]);
    }

    public function redeemLoyaltyPoints(Request $request, Customer $customer)
    {
        $request->validate([
            'points' => 'required|integer|min:1',
            'description' => 'nullable|string',
        ]);

        if (!$customer->redeemLoyaltyPoints(
            $request->points,
            null,
            $request->description ?? 'Manual points redemption'
        )) {
            return response()->json(['error' => 'Insufficient loyalty points.'], 422);
        }

        return response()->json([
            'message' => 'Loyalty points redeemed successfully.',
            'customer' => $customer->fresh(),
        ]);
    }

    public function loyaltyHistory(Customer $customer)
    {
        $transactions = $customer->loyaltyTransactions()
            ->with('order')
            ->latest()
            ->paginate(20);

        return Inertia::render('Customers/LoyaltyHistory', [
            'customer' => $customer,
            'transactions' => $transactions,
        ]);
    }

    public function search(Request $request)
    {
        $query = $request->get('q');
        
        if (!$query) {
            return response()->json([]);
        }

        $customers = Customer::where('name', 'like', "%{$query}%")
            ->orWhere('email', 'like', "%{$query}%")
            ->orWhere('phone', 'like', "%{$query}%")
            ->active()
            ->take(10)
            ->get(['id', 'name', 'email', 'phone', 'loyalty_points']);

        return response()->json($customers);
    }
}
