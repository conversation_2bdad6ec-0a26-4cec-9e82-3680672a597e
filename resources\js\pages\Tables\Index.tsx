import { Head, <PERSON>, router } from '@inertiajs/react';
import { PageProps } from '@/types';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
    Plus,
    Edit,
    Trash2,
    Users,
    MapPin,
    Settings
} from 'lucide-react';
import { useState } from 'react';

interface Table {
    id: number;
    name: string;
    seats: number;
    status: 'available' | 'occupied' | 'reserved' | 'maintenance';
    description?: string;
    position?: { x: number; y: number };
    is_active: boolean;
    current_order?: any[];
    current_reservation?: any[];
}

interface TablesProps extends PageProps {
    tables: Table[];
}

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Tables', href: '/tables' },
];

export default function TablesIndex({ auth, tables }: TablesProps) {
    const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
    const [editingTable, setEditingTable] = useState<Table | null>(null);
    const [formData, setFormData] = useState({
        name: '',
        seats: 2,
        description: '',
        status: 'available' as const,
    });

    const getStatusColor = (status: string) => {
        const colors = {
            available: 'bg-green-100 text-green-800',
            occupied: 'bg-red-100 text-red-800',
            reserved: 'bg-blue-100 text-blue-800',
            maintenance: 'bg-yellow-100 text-yellow-800',
        };
        return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        
        if (editingTable) {
            router.patch(`/tables/${editingTable.id}`, formData, {
                onSuccess: () => {
                    setEditingTable(null);
                    setFormData({ name: '', seats: 2, description: '', status: 'available' });
                }
            });
        } else {
            router.post('/tables', formData, {
                onSuccess: () => {
                    setIsCreateModalOpen(false);
                    setFormData({ name: '', seats: 2, description: '', status: 'available' });
                }
            });
        }
    };

    const handleEdit = (table: Table) => {
        setEditingTable(table);
        setFormData({
            name: table.name,
            seats: table.seats,
            description: table.description || '',
            status: table.status,
        });
    };

    const handleDelete = (table: Table) => {
        if (confirm('Are you sure you want to delete this table?')) {
            router.delete(`/tables/${table.id}`);
        }
    };

    const updateTableStatus = (table: Table, newStatus: string) => {
        router.patch(`/tables/${table.id}/status`, { status: newStatus }, {
            preserveScroll: true,
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Tables Management" />
            
            <div className="flex h-full flex-1 flex-col gap-6 rounded-xl p-6">
                {/* Header */}
                <div className="flex justify-between items-center">
                    <div>
                        <h1 className="text-2xl font-bold">Tables Management</h1>
                        <p className="text-muted-foreground">Manage your restaurant tables and seating arrangements</p>
                    </div>
                    <div className="flex space-x-2">
                        <Link href={route('tables.floor-plan')}>
                            <Button variant="outline">
                                <MapPin className="w-4 h-4 mr-2" />
                                Floor Plan
                            </Button>
                        </Link>
                        <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
                            <DialogTrigger asChild>
                                <Button>
                                    <Plus className="w-4 h-4 mr-2" />
                                    Add Table
                                </Button>
                            </DialogTrigger>
                            <DialogContent>
                                <DialogHeader>
                                    <DialogTitle>Add New Table</DialogTitle>
                                </DialogHeader>
                                <form onSubmit={handleSubmit} className="space-y-4">
                                    <div>
                                        <Label htmlFor="name">Table Name</Label>
                                        <Input
                                            id="name"
                                            value={formData.name}
                                            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                                            placeholder="e.g., Table 1, A1, etc."
                                            required
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="seats">Number of Seats</Label>
                                        <Input
                                            id="seats"
                                            type="number"
                                            min="1"
                                            max="20"
                                            value={formData.seats}
                                            onChange={(e) => setFormData({ ...formData, seats: parseInt(e.target.value) })}
                                            required
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="description">Description (Optional)</Label>
                                        <Input
                                            id="description"
                                            value={formData.description}
                                            onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                                            placeholder="e.g., Window table, VIP section, etc."
                                        />
                                    </div>
                                    <div className="flex justify-end space-x-2">
                                        <Button type="button" variant="outline" onClick={() => setIsCreateModalOpen(false)}>
                                            Cancel
                                        </Button>
                                        <Button type="submit">Create Table</Button>
                                    </div>
                                </form>
                            </DialogContent>
                        </Dialog>
                    </div>
                </div>

                {/* Tables Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    {tables.map((table) => (
                        <Card key={table.id} className="relative">
                            <CardHeader className="pb-3">
                                <div className="flex justify-between items-start">
                                    <CardTitle className="text-lg">{table.name}</CardTitle>
                                    <div className="flex space-x-1">
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() => handleEdit(table)}
                                        >
                                            <Edit className="w-4 h-4" />
                                        </Button>
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() => handleDelete(table)}
                                        >
                                            <Trash2 className="w-4 h-4" />
                                        </Button>
                                    </div>
                                </div>
                                <div className="flex items-center space-x-2">
                                    <Users className="w-4 h-4 text-muted-foreground" />
                                    <span className="text-sm text-muted-foreground">{table.seats} seats</span>
                                </div>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-3">
                                    <div className="flex justify-between items-center">
                                        <span className="text-sm font-medium">Status:</span>
                                        <Select
                                            value={table.status}
                                            onValueChange={(value) => updateTableStatus(table, value)}
                                        >
                                            <SelectTrigger className="w-32">
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="available">Available</SelectItem>
                                                <SelectItem value="occupied">Occupied</SelectItem>
                                                <SelectItem value="reserved">Reserved</SelectItem>
                                                <SelectItem value="maintenance">Maintenance</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                    
                                    {table.description && (
                                        <p className="text-sm text-muted-foreground">{table.description}</p>
                                    )}

                                    {table.current_order && table.current_order.length > 0 && (
                                        <div className="p-2 bg-blue-50 rounded-lg">
                                            <p className="text-xs font-medium text-blue-800">Active Order</p>
                                            <p className="text-xs text-blue-600">Order in progress</p>
                                        </div>
                                    )}

                                    {table.current_reservation && table.current_reservation.length > 0 && (
                                        <div className="p-2 bg-green-50 rounded-lg">
                                            <p className="text-xs font-medium text-green-800">Reserved</p>
                                            <p className="text-xs text-green-600">Upcoming reservation</p>
                                        </div>
                                    )}
                                </div>
                            </CardContent>
                        </Card>
                    ))}
                </div>

                {/* Edit Modal */}
                <Dialog open={!!editingTable} onOpenChange={() => setEditingTable(null)}>
                    <DialogContent>
                        <DialogHeader>
                            <DialogTitle>Edit Table</DialogTitle>
                        </DialogHeader>
                        <form onSubmit={handleSubmit} className="space-y-4">
                            <div>
                                <Label htmlFor="edit-name">Table Name</Label>
                                <Input
                                    id="edit-name"
                                    value={formData.name}
                                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                                    required
                                />
                            </div>
                            <div>
                                <Label htmlFor="edit-seats">Number of Seats</Label>
                                <Input
                                    id="edit-seats"
                                    type="number"
                                    min="1"
                                    max="20"
                                    value={formData.seats}
                                    onChange={(e) => setFormData({ ...formData, seats: parseInt(e.target.value) })}
                                    required
                                />
                            </div>
                            <div>
                                <Label htmlFor="edit-description">Description</Label>
                                <Input
                                    id="edit-description"
                                    value={formData.description}
                                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                                />
                            </div>
                            <div>
                                <Label htmlFor="edit-status">Status</Label>
                                <Select value={formData.status} onValueChange={(value: any) => setFormData({ ...formData, status: value })}>
                                    <SelectTrigger>
                                        <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="available">Available</SelectItem>
                                        <SelectItem value="occupied">Occupied</SelectItem>
                                        <SelectItem value="reserved">Reserved</SelectItem>
                                        <SelectItem value="maintenance">Maintenance</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="flex justify-end space-x-2">
                                <Button type="button" variant="outline" onClick={() => setEditingTable(null)}>
                                    Cancel
                                </Button>
                                <Button type="submit">Update Table</Button>
                            </div>
                        </form>
                    </DialogContent>
                </Dialog>
            </div>
        </AppLayout>
    );
}
