<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class LoyaltyProgram extends Model
{
    use HasFactory;

    protected $fillable = [
        'branch_id',
        'name',
        'description',
        'points_per_dollar',
        'dollar_per_point',
        'minimum_points_to_redeem',
        'start_date',
        'end_date',
        'is_active',
    ];

    protected $casts = [
        'points_per_dollar' => 'integer',
        'dollar_per_point' => 'decimal:2',
        'minimum_points_to_redeem' => 'integer',
        'start_date' => 'date',
        'end_date' => 'date',
        'is_active' => 'boolean',
    ];

    // Relationships
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true)
                    ->where('start_date', '<=', now())
                    ->where(function($q) {
                        $q->whereNull('end_date')
                          ->orWhere('end_date', '>=', now());
                    });
    }

    public function scopeCurrent($query)
    {
        return $query->active();
    }

    // Methods
    public function calculatePointsEarned(float $amount): int
    {
        return (int) floor($amount * $this->points_per_dollar);
    }

    public function calculateRedemptionValue(int $points): float
    {
        return round($points * $this->dollar_per_point, 2);
    }

    public function canRedeemPoints(int $points): bool
    {
        return $points >= $this->minimum_points_to_redeem;
    }

    public function isCurrentlyActive(): bool
    {
        if (!$this->is_active) {
            return false;
        }

        if ($this->start_date > now()) {
            return false;
        }

        if ($this->end_date && $this->end_date < now()) {
            return false;
        }

        return true;
    }

    public function activate(): bool
    {
        return $this->update(['is_active' => true]);
    }

    public function deactivate(): bool
    {
        return $this->update(['is_active' => false]);
    }

    public function extend(\Carbon\Carbon $newEndDate): bool
    {
        if ($newEndDate <= now()) {
            return false;
        }

        return $this->update(['end_date' => $newEndDate]);
    }

    public function getStatusAttribute(): string
    {
        if (!$this->is_active) {
            return 'inactive';
        }

        if ($this->start_date > now()) {
            return 'scheduled';
        }

        if ($this->end_date && $this->end_date < now()) {
            return 'expired';
        }

        return 'active';
    }

    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'active' => 'green',
            'scheduled' => 'blue',
            'expired' => 'red',
            'inactive' => 'gray',
            default => 'gray'
        };
    }

    public function getDaysRemainingAttribute(): ?int
    {
        if (!$this->end_date) {
            return null;
        }

        $days = now()->diffInDays($this->end_date, false);
        return $days >= 0 ? $days : 0;
    }

    public function getConversionRateDisplayAttribute(): string
    {
        return "Earn {$this->points_per_dollar} point" . 
               ($this->points_per_dollar > 1 ? 's' : '') . 
               " per $1 spent";
    }

    public function getRedemptionRateDisplayAttribute(): string
    {
        return "{$this->minimum_points_to_redeem} points = $" . 
               number_format($this->calculateRedemptionValue($this->minimum_points_to_redeem), 2);
    }
}
