<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\MenuItem;
use App\Models\Table;
use App\Models\Customer;
use App\Models\Branch;
use Illuminate\Http\Request;
use Inertia\Inertia;

class OrderController extends Controller
{
    public function index(Request $request)
    {
        $user = $request->user();
        $branchId = $user->branch_id ?? Branch::first()?->id;

        $orders = Order::with(['table', 'customer', 'waiter', 'orderItems.menuItem'])
            ->where('branch_id', $branchId)
            ->when($request->status, function($query, $status) {
                $query->where('status', $status);
            })
            ->when($request->type, function($query, $type) {
                $query->where('type', $type);
            })
            ->when($request->search, function($query, $search) {
                $query->where('order_number', 'like', "%{$search}%")
                     ->orWhereHas('customer', function($q) use ($search) {
                         $q->where('name', 'like', "%{$search}%");
                     });
            })
            ->latest()
            ->paginate(20);

        return Inertia::render('Orders/Index', [
            'orders' => $orders,
            'filters' => $request->only(['status', 'type', 'search']),
        ]);
    }

    public function create(Request $request)
    {
        $user = $request->user();
        $branchId = $user->branch_id ?? Branch::first()?->id;

        $tables = Table::where('branch_id', $branchId)
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        $customers = Customer::active()
            ->orderBy('name')
            ->get();

        return Inertia::render('Orders/Create', [
            'tables' => $tables,
            'customers' => $customers,
        ]);
    }

    public function store(Request $request)
    {
        $request->validate([
            'table_id' => 'nullable|exists:tables,id',
            'customer_id' => 'nullable|exists:customers,id',
            'type' => 'required|in:dine_in,takeaway,delivery',
            'notes' => 'nullable|string',
            'delivery_address' => 'nullable|array',
            'items' => 'required|array|min:1',
            'items.*.menu_item_id' => 'required|exists:menu_items,id',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.customizations' => 'nullable|array',
            'items.*.special_instructions' => 'nullable|string',
        ]);

        $user = $request->user();
        $branchId = $user->branch_id ?? Branch::first()?->id;

        // Generate order number
        $orderNumber = 'ORD-' . now()->format('Ymd') . '-' . str_pad(
            Order::whereDate('created_at', today())->count() + 1, 
            4, 
            '0', 
            STR_PAD_LEFT
        );

        // Create order
        $order = Order::create([
            'order_number' => $orderNumber,
            'branch_id' => $branchId,
            'table_id' => $request->table_id,
            'customer_id' => $request->customer_id,
            'waiter_id' => $user->id,
            'type' => $request->type,
            'notes' => $request->notes,
            'delivery_address' => $request->delivery_address,
        ]);

        // Add order items
        foreach ($request->items as $itemData) {
            $menuItem = MenuItem::find($itemData['menu_item_id']);
            
            if (!$menuItem->canBePrepared($itemData['quantity'])) {
                return redirect()->back()->with('error', "Insufficient inventory for {$menuItem->name}");
            }

            $order->addItem(
                $menuItem,
                $itemData['quantity'],
                $itemData['customizations'] ?? [],
                $itemData['special_instructions'] ?? null
            );
        }

        // Update table status if dine-in
        if ($request->type === 'dine_in' && $request->table_id) {
            Table::find($request->table_id)->updateStatus('occupied');
        }

        return redirect()->route('orders.show', $order)->with('success', 'Order created successfully.');
    }

    public function show(Order $order)
    {
        $order->load(['table', 'customer', 'waiter', 'orderItems.menuItem', 'payments', 'invoice']);

        return Inertia::render('Orders/Show', [
            'order' => $order,
        ]);
    }

    public function updateStatus(Request $request, Order $order)
    {
        $request->validate([
            'status' => 'required|in:pending,confirmed,preparing,ready,served,completed,cancelled',
        ]);

        $order->updateStatus($request->status);

        return response()->json([
            'message' => 'Order status updated successfully.',
            'order' => $order->fresh(['orderItems.menuItem']),
        ]);
    }

    public function updateItemStatus(Request $request, OrderItem $orderItem)
    {
        $request->validate([
            'status' => 'required|in:pending,preparing,ready,served',
        ]);

        if ($request->status === 'preparing') {
            $orderItem->startPreparing();
        } else {
            $orderItem->updateStatus($request->status);
        }

        return response()->json([
            'message' => 'Order item status updated successfully.',
            'orderItem' => $orderItem->fresh(['menuItem']),
        ]);
    }

    public function kitchen(Request $request)
    {
        $user = $request->user();
        $branchId = $user->branch_id ?? Branch::first()?->id;

        $orders = Order::with(['table', 'orderItems.menuItem'])
            ->where('branch_id', $branchId)
            ->whereIn('status', ['confirmed', 'preparing'])
            ->orderBy('created_at')
            ->get();

        return Inertia::render('Orders/Kitchen', [
            'orders' => $orders,
        ]);
    }

    public function pos(Request $request)
    {
        $user = $request->user();
        $branchId = $user->branch_id ?? Branch::first()?->id;

        $tables = Table::where('branch_id', $branchId)
            ->where('is_active', true)
            ->with(['currentOrder'])
            ->orderBy('name')
            ->get();

        return Inertia::render('Orders/POS', [
            'tables' => $tables,
        ]);
    }

    public function addItem(Request $request, Order $order)
    {
        $request->validate([
            'menu_item_id' => 'required|exists:menu_items,id',
            'quantity' => 'required|integer|min:1',
            'customizations' => 'nullable|array',
            'special_instructions' => 'nullable|string',
        ]);

        if (!in_array($order->status, ['pending', 'confirmed'])) {
            return response()->json(['error' => 'Cannot add items to this order.'], 422);
        }

        $menuItem = MenuItem::find($request->menu_item_id);
        
        if (!$menuItem->canBePrepared($request->quantity)) {
            return response()->json(['error' => "Insufficient inventory for {$menuItem->name}"], 422);
        }

        $orderItem = $order->addItem(
            $menuItem,
            $request->quantity,
            $request->customizations ?? [],
            $request->special_instructions
        );

        return response()->json([
            'message' => 'Item added successfully.',
            'orderItem' => $orderItem->load('menuItem'),
            'order' => $order->fresh(),
        ]);
    }

    public function removeItem(OrderItem $orderItem)
    {
        $order = $orderItem->order;

        if (!in_array($order->status, ['pending', 'confirmed'])) {
            return response()->json(['error' => 'Cannot remove items from this order.'], 422);
        }

        $orderItem->delete();
        $order->calculateTotals();

        return response()->json([
            'message' => 'Item removed successfully.',
            'order' => $order->fresh(['orderItems.menuItem']),
        ]);
    }
}
