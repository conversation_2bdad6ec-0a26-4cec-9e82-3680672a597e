<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class PurchaseOrder extends Model
{
    use HasFactory;

    protected $fillable = [
        'po_number',
        'branch_id',
        'supplier_id',
        'created_by',
        'status',
        'total_amount',
        'expected_delivery_date',
        'actual_delivery_date',
        'notes',
    ];

    protected $casts = [
        'total_amount' => 'decimal:2',
        'expected_delivery_date' => 'date',
        'actual_delivery_date' => 'date',
        'created_by' => 'integer',
    ];

    // Relationships
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function items(): HasMany
    {
        return $this->hasMany(PurchaseOrderItem::class);
    }

    // Scopes
    public function scopeDraft($query)
    {
        return $query->where('status', 'draft');
    }

    public function scopeSent($query)
    {
        return $query->where('status', 'sent');
    }

    public function scopeConfirmed($query)
    {
        return $query->where('status', 'confirmed');
    }

    public function scopeReceived($query)
    {
        return $query->where('status', 'received');
    }

    public function scopeCancelled($query)
    {
        return $query->where('status', 'cancelled');
    }

    public function scopeOverdue($query)
    {
        return $query->where('expected_delivery_date', '<', now())
                    ->whereIn('status', ['sent', 'confirmed']);
    }

    // Methods
    public function generatePoNumber(): string
    {
        $prefix = 'PO';
        $date = now()->format('Ymd');
        $sequence = PurchaseOrder::whereDate('created_at', today())->count() + 1;
        
        return sprintf('%s-%s-%04d', $prefix, $date, $sequence);
    }

    public function addItem(InventoryItem $inventoryItem, float $quantity, float $unitPrice): PurchaseOrderItem
    {
        $item = $this->items()->create([
            'inventory_item_id' => $inventoryItem->id,
            'quantity_ordered' => $quantity,
            'unit_price' => $unitPrice,
            'total_price' => $quantity * $unitPrice,
        ]);

        $this->calculateTotal();

        return $item;
    }

    public function calculateTotal(): void
    {
        $total = $this->items()->sum('total_price');
        $this->update(['total_amount' => $total]);
    }

    public function send(): bool
    {
        if ($this->status !== 'draft') {
            return false;
        }

        return $this->update(['status' => 'sent']);
    }

    public function confirm(): bool
    {
        if ($this->status !== 'sent') {
            return false;
        }

        return $this->update(['status' => 'confirmed']);
    }

    public function receive(array $receivedItems = []): bool
    {
        if ($this->status !== 'confirmed') {
            return false;
        }

        foreach ($receivedItems as $itemId => $quantityReceived) {
            $item = $this->items()->find($itemId);
            if ($item) {
                $item->update(['quantity_received' => $quantityReceived]);
                
                // Add to inventory
                $item->inventoryItem->addStock(
                    $quantityReceived,
                    $item->unit_price,
                    'purchase_order',
                    $this->id,
                    "Received from PO {$this->po_number}"
                );
            }
        }

        return $this->update([
            'status' => 'received',
            'actual_delivery_date' => now(),
        ]);
    }

    public function cancel(string $reason = null): bool
    {
        if (!in_array($this->status, ['draft', 'sent', 'confirmed'])) {
            return false;
        }

        $notes = $this->notes;
        if ($reason) {
            $notes .= "\nCancellation reason: " . $reason;
        }

        return $this->update([
            'status' => 'cancelled',
            'notes' => $notes,
        ]);
    }

    public function isOverdue(): bool
    {
        return $this->expected_delivery_date && 
               $this->expected_delivery_date < now() && 
               in_array($this->status, ['sent', 'confirmed']);
    }

    public function canBeEdited(): bool
    {
        return $this->status === 'draft';
    }

    public function canBeCancelled(): bool
    {
        return in_array($this->status, ['draft', 'sent', 'confirmed']);
    }

    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'draft' => 'gray',
            'sent' => 'blue',
            'confirmed' => 'yellow',
            'received' => 'green',
            'cancelled' => 'red',
            default => 'gray'
        };
    }

    public function getCompletionPercentageAttribute(): float
    {
        $totalOrdered = $this->items()->sum('quantity_ordered');
        $totalReceived = $this->items()->sum('quantity_received');

        if ($totalOrdered == 0) {
            return 0;
        }

        return round(($totalReceived / $totalOrdered) * 100, 2);
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($purchaseOrder) {
            if (!$purchaseOrder->po_number) {
                $purchaseOrder->po_number = $purchaseOrder->generatePoNumber();
            }
        });
    }
}
