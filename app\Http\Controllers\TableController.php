<?php

namespace App\Http\Controllers;

use App\Models\Table;
use App\Models\Branch;
use Illuminate\Http\Request;
use Inertia\Inertia;

class TableController extends Controller
{
    public function index(Request $request)
    {
        $user = $request->user();
        $branchId = $user->branch_id ?? Branch::first()?->id;

        $tables = Table::with(['currentOrder', 'currentReservation'])
            ->where('branch_id', $branchId)
            ->orderBy('name')
            ->get();

        return Inertia::render('Tables/Index', [
            'tables' => $tables,
        ]);
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'seats' => 'required|integer|min:1|max:20',
            'description' => 'nullable|string',
            'position' => 'nullable|array',
        ]);

        $user = $request->user();
        $branchId = $user->branch_id ?? Branch::first()?->id;

        $table = Table::create([
            'branch_id' => $branchId,
            'name' => $request->name,
            'seats' => $request->seats,
            'description' => $request->description,
            'position' => $request->position,
        ]);

        return redirect()->back()->with('success', 'Table created successfully.');
    }

    public function update(Request $request, Table $table)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'seats' => 'required|integer|min:1|max:20',
            'description' => 'nullable|string',
            'position' => 'nullable|array',
            'status' => 'required|in:available,occupied,reserved,maintenance',
        ]);

        $table->update($request->only([
            'name', 'seats', 'description', 'position', 'status'
        ]));

        return redirect()->back()->with('success', 'Table updated successfully.');
    }

    public function updateStatus(Request $request, Table $table)
    {
        $request->validate([
            'status' => 'required|in:available,occupied,reserved,maintenance',
        ]);

        $table->updateStatus($request->status);

        return response()->json([
            'message' => 'Table status updated successfully.',
            'table' => $table->fresh(),
        ]);
    }

    public function destroy(Table $table)
    {
        if ($table->orders()->exists()) {
            return redirect()->back()->with('error', 'Cannot delete table with existing orders.');
        }

        $table->delete();

        return redirect()->back()->with('success', 'Table deleted successfully.');
    }

    public function floorPlan(Request $request)
    {
        $user = $request->user();
        $branchId = $user->branch_id ?? Branch::first()?->id;

        $tables = Table::with(['currentOrder.customer', 'currentReservation.customer'])
            ->where('branch_id', $branchId)
            ->where('is_active', true)
            ->get();

        return Inertia::render('Tables/FloorPlan', [
            'tables' => $tables,
        ]);
    }

    public function updatePosition(Request $request, Table $table)
    {
        $request->validate([
            'position' => 'required|array',
            'position.x' => 'required|numeric',
            'position.y' => 'required|numeric',
        ]);

        $table->update([
            'position' => $request->position,
        ]);

        return response()->json([
            'message' => 'Table position updated successfully.',
        ]);
    }
}
