<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Order extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_number',
        'branch_id',
        'table_id',
        'customer_id',
        'waiter_id',
        'type',
        'status',
        'subtotal',
        'tax_amount',
        'discount_amount',
        'total_amount',
        'notes',
        'delivery_address',
        'estimated_ready_time',
        'completed_at',
    ];

    protected $casts = [
        'subtotal' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'delivery_address' => 'array',
        'estimated_ready_time' => 'datetime',
        'completed_at' => 'datetime',
    ];

    // Relationships
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    public function table(): BelongsTo
    {
        return $this->belongsTo(Table::class);
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function waiter(): BelongsTo
    {
        return $this->belongsTo(User::class, 'waiter_id');
    }

    public function orderItems(): HasMany
    {
        return $this->hasMany(OrderItem::class);
    }

    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    public function invoice(): HasOne
    {
        return $this->hasOne(Invoice::class);
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeActive($query)
    {
        return $query->whereIn('status', ['pending', 'confirmed', 'preparing', 'ready', 'served']);
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeDineIn($query)
    {
        return $query->where('type', 'dine_in');
    }

    public function scopeTakeaway($query)
    {
        return $query->where('type', 'takeaway');
    }

    public function scopeDelivery($query)
    {
        return $query->where('type', 'delivery');
    }

    // Methods
    public function calculateTotals(): void
    {
        $subtotal = $this->orderItems()->sum('total_price');
        $taxRate = $this->branch->tax_rate ?? 0;
        $taxAmount = 0;

        foreach ($this->orderItems as $item) {
            if ($item->menuItem->is_taxable) {
                $taxAmount += ($item->total_price * ($taxRate / 100));
            }
        }

        $this->update([
            'subtotal' => $subtotal,
            'tax_amount' => round($taxAmount, 2),
            'total_amount' => round($subtotal + $taxAmount - $this->discount_amount, 2),
        ]);
    }

    public function addItem(MenuItem $menuItem, int $quantity, array $customizations = [], string $specialInstructions = null): OrderItem
    {
        $orderItem = $this->orderItems()->create([
            'menu_item_id' => $menuItem->id,
            'quantity' => $quantity,
            'unit_price' => $menuItem->price,
            'total_price' => $menuItem->price * $quantity,
            'customizations' => $customizations,
            'special_instructions' => $specialInstructions,
        ]);

        $this->calculateTotals();

        return $orderItem;
    }

    public function updateStatus(string $status): bool
    {
        $validStatuses = ['pending', 'confirmed', 'preparing', 'ready', 'served', 'completed', 'cancelled'];
        
        if (!in_array($status, $validStatuses)) {
            return false;
        }

        $updated = $this->update(['status' => $status]);

        if ($status === 'completed') {
            $this->update(['completed_at' => now()]);
            
            // Update customer's last visit
            if ($this->customer) {
                $this->customer->updateLastVisit();
            }

            // Update table status if dine-in
            if ($this->type === 'dine_in' && $this->table) {
                $this->table->updateStatus('available');
            }
        }

        return $updated;
    }

    public function canBeCancelled(): bool
    {
        return in_array($this->status, ['pending', 'confirmed']);
    }

    public function getTotalPaidAttribute(): float
    {
        return $this->payments()->where('status', 'completed')->sum('amount');
    }

    public function getRemainingAmountAttribute(): float
    {
        return max(0, $this->total_amount - $this->total_paid);
    }

    public function isFullyPaid(): bool
    {
        return $this->remaining_amount <= 0;
    }
}
