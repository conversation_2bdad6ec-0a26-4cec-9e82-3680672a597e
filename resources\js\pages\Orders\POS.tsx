import { Head, router } from '@inertiajs/react';
import { PageProps } from '@/types';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
    Plus,
    Minus,
    ShoppingCart,
    Users,
    Table as TableIcon,
    Trash2,
    CreditCard,
    DollarSign
} from 'lucide-react';
import { useState } from 'react';

interface Table {
    id: number;
    name: string;
    seats: number;
    status: string;
    current_order?: any;
}

interface POSProps extends PageProps {
    tables: Table[];
}

interface CartItem {
    id: string;
    name: string;
    price: number;
    quantity: number;
    total: number;
}

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Orders', href: '/orders' },
    { title: 'POS', href: '/orders-pos' },
];

export default function POS({ auth, tables }: POSProps) {
    const [selectedTable, setSelectedTable] = useState<Table | null>(null);
    const [orderType, setOrderType] = useState<'dine_in' | 'takeaway' | 'delivery'>('dine_in');
    const [cart, setCart] = useState<CartItem[]>([]);
    const [selectedCategory, setSelectedCategory] = useState<string>('all');

    // Mock menu items - in real app, this would come from props
    const menuCategories = [
        { id: 'all', name: 'All Items' },
        { id: 'appetizers', name: 'Appetizers' },
        { id: 'mains', name: 'Main Courses' },
        { id: 'desserts', name: 'Desserts' },
        { id: 'beverages', name: 'Beverages' },
    ];

    const menuItems = [
        { id: '1', name: 'Caesar Salad', price: 12.99, category: 'appetizers' },
        { id: '2', name: 'Grilled Chicken', price: 18.99, category: 'mains' },
        { id: '3', name: 'Pasta Carbonara', price: 16.99, category: 'mains' },
        { id: '4', name: 'Chocolate Cake', price: 8.99, category: 'desserts' },
        { id: '5', name: 'Coffee', price: 3.99, category: 'beverages' },
        { id: '6', name: 'Fresh Juice', price: 4.99, category: 'beverages' },
    ];

    const filteredItems = selectedCategory === 'all' 
        ? menuItems 
        : menuItems.filter(item => item.category === selectedCategory);

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(amount);
    };

    const addToCart = (item: typeof menuItems[0]) => {
        const existingItem = cart.find(cartItem => cartItem.id === item.id);
        
        if (existingItem) {
            setCart(cart.map(cartItem => 
                cartItem.id === item.id 
                    ? { ...cartItem, quantity: cartItem.quantity + 1, total: (cartItem.quantity + 1) * cartItem.price }
                    : cartItem
            ));
        } else {
            setCart([...cart, {
                id: item.id,
                name: item.name,
                price: item.price,
                quantity: 1,
                total: item.price
            }]);
        }
    };

    const updateQuantity = (itemId: string, newQuantity: number) => {
        if (newQuantity <= 0) {
            setCart(cart.filter(item => item.id !== itemId));
        } else {
            setCart(cart.map(item => 
                item.id === itemId 
                    ? { ...item, quantity: newQuantity, total: newQuantity * item.price }
                    : item
            ));
        }
    };

    const removeFromCart = (itemId: string) => {
        setCart(cart.filter(item => item.id !== itemId));
    };

    const getCartTotal = () => {
        return cart.reduce((total, item) => total + item.total, 0);
    };

    const getTaxAmount = () => {
        return getCartTotal() * 0.1; // 10% tax
    };

    const getFinalTotal = () => {
        return getCartTotal() + getTaxAmount();
    };

    const handlePlaceOrder = () => {
        if (cart.length === 0) {
            alert('Please add items to cart');
            return;
        }

        if (orderType === 'dine_in' && !selectedTable) {
            alert('Please select a table for dine-in orders');
            return;
        }

        // In real app, this would submit to the backend
        const orderData = {
            type: orderType,
            table_id: selectedTable?.id,
            items: cart.map(item => ({
                menu_item_id: item.id,
                quantity: item.quantity,
                unit_price: item.price,
                total_price: item.total
            }))
        };

        console.log('Placing order:', orderData);
        
        // Reset cart and selections
        setCart([]);
        setSelectedTable(null);
        
        alert('Order placed successfully!');
    };

    const getTableStatusColor = (status: string) => {
        const colors = {
            available: 'bg-green-100 text-green-800 border-green-200',
            occupied: 'bg-red-100 text-red-800 border-red-200',
            reserved: 'bg-blue-100 text-blue-800 border-blue-200',
            maintenance: 'bg-yellow-100 text-yellow-800 border-yellow-200',
        };
        return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800 border-gray-200';
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Point of Sale" />
            
            <div className="flex h-full flex-1 gap-6 rounded-xl p-6">
                {/* Left Panel - Menu */}
                <div className="flex-1 space-y-6">
                    {/* Order Type Selection */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Order Type</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="flex space-x-2">
                                <Button
                                    variant={orderType === 'dine_in' ? 'default' : 'outline'}
                                    onClick={() => setOrderType('dine_in')}
                                    className="flex-1"
                                >
                                    <TableIcon className="w-4 h-4 mr-2" />
                                    Dine In
                                </Button>
                                <Button
                                    variant={orderType === 'takeaway' ? 'default' : 'outline'}
                                    onClick={() => setOrderType('takeaway')}
                                    className="flex-1"
                                >
                                    <ShoppingCart className="w-4 h-4 mr-2" />
                                    Takeaway
                                </Button>
                                <Button
                                    variant={orderType === 'delivery' ? 'default' : 'outline'}
                                    onClick={() => setOrderType('delivery')}
                                    className="flex-1"
                                >
                                    <Users className="w-4 h-4 mr-2" />
                                    Delivery
                                </Button>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Table Selection (for dine-in) */}
                    {orderType === 'dine_in' && (
                        <Card>
                            <CardHeader>
                                <CardTitle>Select Table</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="grid grid-cols-4 gap-2">
                                    {tables.map((table) => (
                                        <Button
                                            key={table.id}
                                            variant={selectedTable?.id === table.id ? 'default' : 'outline'}
                                            onClick={() => table.status === 'available' && setSelectedTable(table)}
                                            disabled={table.status !== 'available'}
                                            className={`h-16 ${getTableStatusColor(table.status)}`}
                                        >
                                            <div className="text-center">
                                                <div className="font-semibold">{table.name}</div>
                                                <div className="text-xs">{table.seats} seats</div>
                                            </div>
                                        </Button>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>
                    )}

                    {/* Menu Categories */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Menu Categories</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="flex flex-wrap gap-2">
                                {menuCategories.map((category) => (
                                    <Button
                                        key={category.id}
                                        variant={selectedCategory === category.id ? 'default' : 'outline'}
                                        onClick={() => setSelectedCategory(category.id)}
                                        size="sm"
                                    >
                                        {category.name}
                                    </Button>
                                ))}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Menu Items */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Menu Items</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-2 lg:grid-cols-3 gap-4">
                                {filteredItems.map((item) => (
                                    <Button
                                        key={item.id}
                                        variant="outline"
                                        onClick={() => addToCart(item)}
                                        className="h-20 flex flex-col justify-center p-4"
                                    >
                                        <div className="font-semibold text-sm">{item.name}</div>
                                        <div className="text-green-600 font-bold">{formatCurrency(item.price)}</div>
                                    </Button>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Right Panel - Cart */}
                <div className="w-96">
                    <Card className="h-full flex flex-col">
                        <CardHeader>
                            <CardTitle className="flex items-center">
                                <ShoppingCart className="w-5 h-5 mr-2" />
                                Order Cart ({cart.length})
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="flex-1 flex flex-col">
                            {/* Cart Items */}
                            <div className="flex-1 space-y-3 mb-6">
                                {cart.map((item) => (
                                    <div key={item.id} className="flex items-center justify-between p-3 border rounded-lg">
                                        <div className="flex-1">
                                            <p className="font-medium text-sm">{item.name}</p>
                                            <p className="text-green-600 font-semibold">{formatCurrency(item.price)}</p>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => updateQuantity(item.id, item.quantity - 1)}
                                            >
                                                <Minus className="w-3 h-3" />
                                            </Button>
                                            <span className="w-8 text-center font-medium">{item.quantity}</span>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => updateQuantity(item.id, item.quantity + 1)}
                                            >
                                                <Plus className="w-3 h-3" />
                                            </Button>
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                onClick={() => removeFromCart(item.id)}
                                            >
                                                <Trash2 className="w-3 h-3" />
                                            </Button>
                                        </div>
                                    </div>
                                ))}
                                
                                {cart.length === 0 && (
                                    <div className="text-center text-muted-foreground py-8">
                                        <ShoppingCart className="w-12 h-12 mx-auto mb-2 opacity-50" />
                                        <p>Cart is empty</p>
                                        <p className="text-sm">Add items from the menu</p>
                                    </div>
                                )}
                            </div>

                            {/* Order Summary */}
                            {cart.length > 0 && (
                                <div className="space-y-4 border-t pt-4">
                                    <div className="space-y-2">
                                        <div className="flex justify-between">
                                            <span>Subtotal:</span>
                                            <span>{formatCurrency(getCartTotal())}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span>Tax (10%):</span>
                                            <span>{formatCurrency(getTaxAmount())}</span>
                                        </div>
                                        <div className="flex justify-between font-bold text-lg border-t pt-2">
                                            <span>Total:</span>
                                            <span>{formatCurrency(getFinalTotal())}</span>
                                        </div>
                                    </div>

                                    <Button 
                                        onClick={handlePlaceOrder}
                                        className="w-full"
                                        size="lg"
                                    >
                                        <CreditCard className="w-4 h-4 mr-2" />
                                        Place Order
                                    </Button>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
