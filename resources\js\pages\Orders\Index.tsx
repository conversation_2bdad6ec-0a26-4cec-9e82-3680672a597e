import { Head, <PERSON>, router } from '@inertiajs/react';
import { PageProps } from '@/types';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
    Plus,
    Search,
    Filter,
    Eye,
    Clock,
    DollarSign,
    Users,
    Table as TableIcon,
    ChefHat,
    ShoppingCart
} from 'lucide-react';
import { useState } from 'react';

interface Order {
    id: number;
    order_number: string;
    status: string;
    type: string;
    subtotal: number;
    tax_amount: number;
    discount_amount: number;
    total_amount: number;
    created_at: string;
    estimated_ready_time?: string;
    table?: { id: number; name: string };
    customer?: { id: number; name: string };
    waiter?: { id: number; name: string };
    order_items: Array<{
        id: number;
        quantity: number;
        unit_price: number;
        total_price: number;
        status: string;
        menu_item: {
            id: number;
            name: string;
        };
    }>;
}

interface OrdersProps extends PageProps {
    orders: {
        data: Order[];
        links: any;
        meta: any;
    };
    filters: {
        status?: string;
        type?: string;
        search?: string;
    };
}

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Orders', href: '/orders' },
];

export default function OrdersIndex({ auth, orders, filters }: OrdersProps) {
    const [searchTerm, setSearchTerm] = useState(filters.search || '');
    const [statusFilter, setStatusFilter] = useState(filters.status || '');
    const [typeFilter, setTypeFilter] = useState(filters.type || '');

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-PK', {
            style: 'currency',
            currency: 'PKR'
        }).format(amount);
    };

    const formatTime = (datetime: string) => {
        return new Date(datetime).toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const formatDate = (datetime: string) => {
        return new Date(datetime).toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const getStatusColor = (status: string) => {
        const colors: Record<string, string> = {
            pending: 'bg-yellow-100 text-yellow-800',
            confirmed: 'bg-blue-100 text-blue-800',
            preparing: 'bg-orange-100 text-orange-800',
            ready: 'bg-green-100 text-green-800',
            served: 'bg-purple-100 text-purple-800',
            completed: 'bg-gray-100 text-gray-800',
            cancelled: 'bg-red-100 text-red-800',
        };
        return colors[status] || 'bg-gray-100 text-gray-800';
    };

    const getTypeIcon = (type: string) => {
        switch (type) {
            case 'dine_in':
                return <TableIcon className="w-4 h-4" />;
            case 'takeaway':
                return <ShoppingCart className="w-4 h-4" />;
            case 'delivery':
                return <Users className="w-4 h-4" />;
            default:
                return <ShoppingCart className="w-4 h-4" />;
        }
    };

    const handleSearch = () => {
        router.get('/orders', {
            search: searchTerm,
            status: statusFilter,
            type: typeFilter,
        }, {
            preserveState: true,
            replace: true,
        });
    };

    const updateOrderStatus = (orderId: number, newStatus: string) => {
        router.patch(`/orders/${orderId}/status`, { status: newStatus }, {
            preserveScroll: true,
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Orders Management" />
            
            <div className="flex h-full flex-1 flex-col gap-6 rounded-xl p-6">
                {/* Header */}
                <div className="flex justify-between items-center">
                    <div>
                        <h1 className="text-2xl font-bold">Orders Management</h1>
                        <p className="text-muted-foreground">Track and manage all restaurant orders</p>
                    </div>
                    <div className="flex space-x-2">
                        <Link href={route('orders.kitchen')}>
                            <Button variant="outline">
                                <ChefHat className="w-4 h-4 mr-2" />
                                Kitchen View
                            </Button>
                        </Link>
                        <Link href={route('orders.pos')}>
                            <Button variant="outline">
                                <ShoppingCart className="w-4 h-4 mr-2" />
                                POS
                            </Button>
                        </Link>
                        <Link href={route('orders.create')}>
                            <Button>
                                <Plus className="w-4 h-4 mr-2" />
                                New Order
                            </Button>
                        </Link>
                    </div>
                </div>

                {/* Filters */}
                <Card>
                    <CardContent className="pt-6">
                        <div className="flex flex-col md:flex-row gap-4">
                            <div className="flex-1">
                                <div className="relative">
                                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                                    <Input
                                        placeholder="Search orders by number or customer..."
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                        className="pl-10"
                                        onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                                    />
                                </div>
                            </div>
                            <Select value={statusFilter} onValueChange={setStatusFilter}>
                                <SelectTrigger className="w-40">
                                    <SelectValue placeholder="All Statuses" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="">All Statuses</SelectItem>
                                    <SelectItem value="pending">Pending</SelectItem>
                                    <SelectItem value="confirmed">Confirmed</SelectItem>
                                    <SelectItem value="preparing">Preparing</SelectItem>
                                    <SelectItem value="ready">Ready</SelectItem>
                                    <SelectItem value="served">Served</SelectItem>
                                    <SelectItem value="completed">Completed</SelectItem>
                                    <SelectItem value="cancelled">Cancelled</SelectItem>
                                </SelectContent>
                            </Select>
                            <Select value={typeFilter} onValueChange={setTypeFilter}>
                                <SelectTrigger className="w-40">
                                    <SelectValue placeholder="All Types" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="">All Types</SelectItem>
                                    <SelectItem value="dine_in">Dine In</SelectItem>
                                    <SelectItem value="takeaway">Takeaway</SelectItem>
                                    <SelectItem value="delivery">Delivery</SelectItem>
                                </SelectContent>
                            </Select>
                            <Button onClick={handleSearch}>
                                <Filter className="w-4 h-4 mr-2" />
                                Filter
                            </Button>
                        </div>
                    </CardContent>
                </Card>

                {/* Orders List */}
                <div className="space-y-4">
                    {orders.data.map((order) => (
                        <Card key={order.id}>
                            <CardContent className="pt-6">
                                <div className="flex justify-between items-start">
                                    <div className="flex-1">
                                        <div className="flex items-center space-x-4 mb-2">
                                            <h3 className="text-lg font-semibold">{order.order_number}</h3>
                                            <div className="flex items-center space-x-1">
                                                {getTypeIcon(order.type)}
                                                <span className="text-sm text-muted-foreground capitalize">
                                                    {order.type.replace('_', ' ')}
                                                </span>
                                            </div>
                                            <Badge className={getStatusColor(order.status)}>
                                                {order.status}
                                            </Badge>
                                        </div>
                                        
                                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                                            <div>
                                                <p className="text-muted-foreground">Customer</p>
                                                <p className="font-medium">
                                                    {order.customer?.name || 'Walk-in Customer'}
                                                </p>
                                            </div>
                                            <div>
                                                <p className="text-muted-foreground">Table</p>
                                                <p className="font-medium">
                                                    {order.table?.name || 'No table assigned'}
                                                </p>
                                            </div>
                                            <div>
                                                <p className="text-muted-foreground">Waiter</p>
                                                <p className="font-medium">
                                                    {order.waiter?.name || 'Not assigned'}
                                                </p>
                                            </div>
                                        </div>

                                        <div className="mt-3">
                                            <p className="text-sm text-muted-foreground">Items:</p>
                                            <div className="flex flex-wrap gap-2 mt-1">
                                                {order.order_items.slice(0, 3).map((item) => (
                                                    <Badge key={item.id} variant="outline" className="text-xs">
                                                        {item.quantity}x {item.menu_item.name}
                                                    </Badge>
                                                ))}
                                                {order.order_items.length > 3 && (
                                                    <Badge variant="outline" className="text-xs">
                                                        +{order.order_items.length - 3} more
                                                    </Badge>
                                                )}
                                            </div>
                                        </div>
                                    </div>

                                    <div className="text-right space-y-2">
                                        <div>
                                            <p className="text-lg font-bold">{formatCurrency(order.total_amount)}</p>
                                            <p className="text-xs text-muted-foreground">
                                                {formatDate(order.created_at)}
                                            </p>
                                        </div>
                                        
                                        <div className="flex space-x-2">
                                            <Link href={route('orders.show', order.id)}>
                                                <Button variant="outline" size="sm">
                                                    <Eye className="w-4 h-4" />
                                                </Button>
                                            </Link>
                                            {order.status !== 'completed' && order.status !== 'cancelled' && (
                                                <Select
                                                    value={order.status}
                                                    onValueChange={(value) => updateOrderStatus(order.id, value)}
                                                >
                                                    <SelectTrigger className="w-32">
                                                        <SelectValue />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem value="pending">Pending</SelectItem>
                                                        <SelectItem value="confirmed">Confirmed</SelectItem>
                                                        <SelectItem value="preparing">Preparing</SelectItem>
                                                        <SelectItem value="ready">Ready</SelectItem>
                                                        <SelectItem value="served">Served</SelectItem>
                                                        <SelectItem value="completed">Completed</SelectItem>
                                                        <SelectItem value="cancelled">Cancelled</SelectItem>
                                                    </SelectContent>
                                                </Select>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    ))}
                </div>

                {orders.data.length === 0 && (
                    <Card>
                        <CardContent className="text-center py-12">
                            <h3 className="text-lg font-semibold mb-2">No Orders Found</h3>
                            <p className="text-muted-foreground mb-4">
                                {filters.search || filters.status || filters.type 
                                    ? 'No orders match your current filters.'
                                    : 'No orders have been placed yet.'
                                }
                            </p>
                            <Link href={route('orders.create')}>
                                <Button>
                                    <Plus className="w-4 h-4 mr-2" />
                                    Create First Order
                                </Button>
                            </Link>
                        </CardContent>
                    </Card>
                )}

                {/* Pagination would go here */}
            </div>
        </AppLayout>
    );
}
