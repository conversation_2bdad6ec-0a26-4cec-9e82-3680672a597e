<?php

namespace Database\Seeders;

use App\Models\Branch;
use App\Models\Customer;
use App\Models\InventoryItem;
use App\Models\LoyaltyProgram;
use App\Models\MenuCategory;
use App\Models\MenuItem;
use App\Models\StaffRole;
use App\Models\Supplier;
use App\Models\Table;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class RestaurantSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // Create main branch
        $branch = Branch::create([
            'name' => 'Habibi Café Main Branch',
            'code' => 'MAIN',
            'address' => '123 Main Street, Downtown, City 12345',
            'phone' => '+****************',
            'email' => '<EMAIL>',
            'tax_rate' => 10.00,
            'business_hours' => [
                'monday' => ['open' => '08:00', 'close' => '22:00'],
                'tuesday' => ['open' => '08:00', 'close' => '22:00'],
                'wednesday' => ['open' => '08:00', 'close' => '22:00'],
                'thursday' => ['open' => '08:00', 'close' => '22:00'],
                'friday' => ['open' => '08:00', 'close' => '23:00'],
                'saturday' => ['open' => '09:00', 'close' => '23:00'],
                'sunday' => ['open' => '09:00', 'close' => '21:00'],
            ],
            'is_active' => true,
        ]);

        // Create staff roles
        $managerRole = StaffRole::create([
            'name' => 'Manager',
            'description' => 'Restaurant manager with full access',
            'permissions' => StaffRole::getDefaultPermissions('manager'),
            'is_active' => true,
        ]);

        $waiterRole = StaffRole::create([
            'name' => 'Waiter',
            'description' => 'Waiter/Waitress for serving customers',
            'permissions' => StaffRole::getDefaultPermissions('waiter'),
            'is_active' => true,
        ]);

        $chefRole = StaffRole::create([
            'name' => 'Chef',
            'description' => 'Kitchen chef for food preparation',
            'permissions' => StaffRole::getDefaultPermissions('chef'),
            'is_active' => true,
        ]);

        $cashierRole = StaffRole::create([
            'name' => 'Cashier',
            'description' => 'Cashier for payment processing',
            'permissions' => StaffRole::getDefaultPermissions('cashier'),
            'is_active' => true,
        ]);

        // Create staff users
        User::create([
            'name' => 'John Manager',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'branch_id' => $branch->id,
            'staff_role_id' => $managerRole->id,
            'employee_id' => 'EMP001',
            'phone' => '+****************',
            'hire_date' => now()->subMonths(12),
            'hourly_rate' => 25.00,
            'employment_status' => 'active',
            'is_staff' => true,
        ]);

        User::create([
            'name' => 'Sarah Waiter',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'branch_id' => $branch->id,
            'staff_role_id' => $waiterRole->id,
            'employee_id' => 'EMP002',
            'phone' => '+****************',
            'hire_date' => now()->subMonths(6),
            'hourly_rate' => 15.00,
            'employment_status' => 'active',
            'is_staff' => true,
        ]);

        User::create([
            'name' => 'Mike Chef',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'branch_id' => $branch->id,
            'staff_role_id' => $chefRole->id,
            'employee_id' => 'EMP003',
            'phone' => '+****************',
            'hire_date' => now()->subMonths(8),
            'hourly_rate' => 20.00,
            'employment_status' => 'active',
            'is_staff' => true,
        ]);

        // Create tables
        $tables = [
            ['name' => 'T1', 'seats' => 2, 'position' => ['x' => 100, 'y' => 100]],
            ['name' => 'T2', 'seats' => 2, 'position' => ['x' => 200, 'y' => 100]],
            ['name' => 'T3', 'seats' => 4, 'position' => ['x' => 300, 'y' => 100]],
            ['name' => 'T4', 'seats' => 4, 'position' => ['x' => 100, 'y' => 200]],
            ['name' => 'T5', 'seats' => 6, 'position' => ['x' => 200, 'y' => 200]],
            ['name' => 'T6', 'seats' => 8, 'position' => ['x' => 300, 'y' => 200]],
            ['name' => 'T7', 'seats' => 2, 'position' => ['x' => 100, 'y' => 300]],
            ['name' => 'T8', 'seats' => 4, 'position' => ['x' => 200, 'y' => 300]],
        ];

        foreach ($tables as $tableData) {
            Table::create([
                'branch_id' => $branch->id,
                'name' => $tableData['name'],
                'seats' => $tableData['seats'],
                'status' => 'available',
                'position' => $tableData['position'],
                'is_active' => true,
            ]);
        }

        // Create menu categories
        $appetizers = MenuCategory::create([
            'branch_id' => $branch->id,
            'name' => 'Appetizers',
            'description' => 'Start your meal with our delicious appetizers',
            'sort_order' => 1,
            'is_active' => true,
        ]);

        $mains = MenuCategory::create([
            'branch_id' => $branch->id,
            'name' => 'Main Courses',
            'description' => 'Hearty main dishes to satisfy your hunger',
            'sort_order' => 2,
            'is_active' => true,
        ]);

        $desserts = MenuCategory::create([
            'branch_id' => $branch->id,
            'name' => 'Desserts',
            'description' => 'Sweet treats to end your meal perfectly',
            'sort_order' => 3,
            'is_active' => true,
        ]);

        $beverages = MenuCategory::create([
            'branch_id' => $branch->id,
            'name' => 'Beverages',
            'description' => 'Refreshing drinks and hot beverages',
            'sort_order' => 4,
            'is_active' => true,
        ]);

        // Create menu items
        $menuItems = [
            // Appetizers
            [
                'category' => $appetizers,
                'name' => 'Hummus & Pita',
                'description' => 'Creamy hummus served with warm pita bread',
                'price' => 450,
                'dietary_info' => ['vegetarian', 'vegan'],
                'preparation_time' => 5,
            ],
            [
                'category' => $appetizers,
                'name' => 'Falafel Plate',
                'description' => 'Crispy falafel with tahini sauce',
                'price' => 550,
                'dietary_info' => ['vegetarian', 'vegan'],
                'preparation_time' => 10,
            ],
            [
                'category' => $appetizers,
                'name' => 'Stuffed Grape Leaves',
                'description' => 'Traditional dolmas with rice and herbs',
                'price' => 500,
                'dietary_info' => ['vegetarian', 'vegan'],
                'preparation_time' => 8,
            ],

            // Main Courses
            [
                'category' => $mains,
                'name' => 'Chicken Shawarma',
                'description' => 'Marinated chicken with garlic sauce and vegetables',
                'price' => 850,
                'dietary_info' => ['gluten-free'],
                'preparation_time' => 15,
            ],
            [
                'category' => $mains,
                'name' => 'Lamb Kebab',
                'description' => 'Grilled lamb skewers with rice and salad',
                'price' => 1150,
                'dietary_info' => ['gluten-free'],
                'preparation_time' => 20,
            ],
            [
                'category' => $mains,
                'name' => 'Vegetarian Moussaka',
                'description' => 'Layers of eggplant, lentils, and béchamel sauce',
                'price' => 950,
                'dietary_info' => ['vegetarian'],
                'preparation_time' => 25,
            ],

            // Desserts
            [
                'category' => $desserts,
                'name' => 'Baklava',
                'description' => 'Flaky pastry with nuts and honey',
                'price' => 350,
                'dietary_info' => ['vegetarian'],
                'preparation_time' => 3,
            ],
            [
                'category' => $desserts,
                'name' => 'Kunafa',
                'description' => 'Sweet cheese pastry with syrup',
                'price' => 400,
                'dietary_info' => ['vegetarian'],
                'preparation_time' => 5,
            ],

            // Beverages
            [
                'category' => $beverages,
                'name' => 'Turkish Coffee',
                'description' => 'Traditional strong coffee',
                'price' => 250,
                'dietary_info' => ['vegan'],
                'preparation_time' => 5,
            ],
            [
                'category' => $beverages,
                'name' => 'Fresh Mint Tea',
                'description' => 'Refreshing mint tea',
                'price' => 200,
                'dietary_info' => ['vegan'],
                'preparation_time' => 3,
            ],
            [
                'category' => $beverages,
                'name' => 'Fresh Orange Juice',
                'description' => 'Freshly squeezed orange juice',
                'price' => 300,
                'dietary_info' => ['vegan'],
                'preparation_time' => 2,
            ],
        ];

        foreach ($menuItems as $index => $itemData) {
            MenuItem::create([
                'branch_id' => $branch->id,
                'menu_category_id' => $itemData['category']->id,
                'name' => $itemData['name'],
                'description' => $itemData['description'],
                'price' => $itemData['price'],
                'dietary_info' => $itemData['dietary_info'],
                'is_taxable' => true,
                'preparation_time' => $itemData['preparation_time'],
                'is_available' => true,
                'is_active' => true,
                'sort_order' => $index + 1,
            ]);
        }

        // Create customers
        $customers = [
            [
                'name' => 'Ahmed Hassan',
                'email' => '<EMAIL>',
                'phone' => '+****************',
                'loyalty_points' => 150,
            ],
            [
                'name' => 'Fatima Al-Zahra',
                'email' => '<EMAIL>',
                'phone' => '+****************',
                'loyalty_points' => 200,
            ],
            [
                'name' => 'Omar Khalil',
                'email' => '<EMAIL>',
                'phone' => '+****************',
                'loyalty_points' => 75,
            ],
        ];

        foreach ($customers as $customerData) {
            Customer::create($customerData);
        }

        // Create loyalty program
        LoyaltyProgram::create([
            'branch_id' => $branch->id,
            'name' => 'Habibi Rewards',
            'description' => 'Earn points with every purchase and redeem for discounts',
            'points_per_dollar' => 1,
            'dollar_per_point' => 0.01,
            'minimum_points_to_redeem' => 100,
            'start_date' => now(),
            'end_date' => null,
            'is_active' => true,
        ]);

        // Create suppliers
        $suppliers = [
            [
                'name' => 'Fresh Produce Co.',
                'contact_person' => 'John Smith',
                'email' => '<EMAIL>',
                'phone' => '+****************',
                'address' => '456 Supplier St, City 12345',
            ],
            [
                'name' => 'Mediterranean Imports',
                'contact_person' => 'Maria Garcia',
                'email' => '<EMAIL>',
                'phone' => '+****************',
                'address' => '789 Import Ave, City 12345',
            ],
        ];

        foreach ($suppliers as $supplierData) {
            Supplier::create($supplierData);
        }

        // Create inventory items
        $inventoryItems = [
            ['name' => 'Chickpeas', 'sku' => 'INV001', 'unit' => 'kg', 'current_stock' => 50, 'minimum_stock' => 10, 'unit_cost' => 3.50, 'category' => 'Legumes'],
            ['name' => 'Tahini', 'sku' => 'INV002', 'unit' => 'jar', 'current_stock' => 20, 'minimum_stock' => 5, 'unit_cost' => 8.99, 'category' => 'Condiments'],
            ['name' => 'Pita Bread', 'sku' => 'INV003', 'unit' => 'pack', 'current_stock' => 30, 'minimum_stock' => 10, 'unit_cost' => 2.50, 'category' => 'Bakery'],
            ['name' => 'Chicken Breast', 'sku' => 'INV004', 'unit' => 'kg', 'current_stock' => 25, 'minimum_stock' => 5, 'unit_cost' => 12.99, 'category' => 'Meat'],
            ['name' => 'Lamb Shoulder', 'sku' => 'INV005', 'unit' => 'kg', 'current_stock' => 15, 'minimum_stock' => 3, 'unit_cost' => 18.99, 'category' => 'Meat'],
            ['name' => 'Eggplant', 'sku' => 'INV006', 'unit' => 'kg', 'current_stock' => 8, 'minimum_stock' => 5, 'unit_cost' => 4.99, 'category' => 'Vegetables'],
            ['name' => 'Coffee Beans', 'sku' => 'INV007', 'unit' => 'kg', 'current_stock' => 12, 'minimum_stock' => 3, 'unit_cost' => 15.99, 'category' => 'Beverages'],
            ['name' => 'Mint Leaves', 'sku' => 'INV008', 'unit' => 'bunch', 'current_stock' => 2, 'minimum_stock' => 5, 'unit_cost' => 1.99, 'category' => 'Herbs'],
        ];

        foreach ($inventoryItems as $itemData) {
            InventoryItem::create([
                'branch_id' => $branch->id,
                'name' => $itemData['name'],
                'sku' => $itemData['sku'],
                'unit' => $itemData['unit'],
                'current_stock' => $itemData['current_stock'],
                'minimum_stock' => $itemData['minimum_stock'],
                'unit_cost' => $itemData['unit_cost'],
                'category' => $itemData['category'],
                'is_active' => true,
            ]);
        }

        $this->command->info('Restaurant seeder completed successfully!');
    }
}
