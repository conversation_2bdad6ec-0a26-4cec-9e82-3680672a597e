<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PurchaseOrderItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'purchase_order_id',
        'inventory_item_id',
        'quantity_ordered',
        'quantity_received',
        'unit_price',
        'total_price',
    ];

    protected $casts = [
        'quantity_ordered' => 'decimal:3',
        'quantity_received' => 'decimal:3',
        'unit_price' => 'decimal:2',
        'total_price' => 'decimal:2',
    ];

    // Relationships
    public function purchaseOrder(): BelongsTo
    {
        return $this->belongsTo(PurchaseOrder::class);
    }

    public function inventoryItem(): BelongsTo
    {
        return $this->belongsTo(InventoryItem::class);
    }

    // Methods
    public function getQuantityPendingAttribute(): float
    {
        return $this->quantity_ordered - $this->quantity_received;
    }

    public function getReceivedPercentageAttribute(): float
    {
        if ($this->quantity_ordered == 0) {
            return 0;
        }

        return round(($this->quantity_received / $this->quantity_ordered) * 100, 2);
    }

    public function isFullyReceived(): bool
    {
        return $this->quantity_received >= $this->quantity_ordered;
    }

    public function isPartiallyReceived(): bool
    {
        return $this->quantity_received > 0 && $this->quantity_received < $this->quantity_ordered;
    }

    public function isNotReceived(): bool
    {
        return $this->quantity_received == 0;
    }

    public function getStatusAttribute(): string
    {
        if ($this->isFullyReceived()) {
            return 'received';
        } elseif ($this->isPartiallyReceived()) {
            return 'partial';
        } else {
            return 'pending';
        }
    }

    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'received' => 'green',
            'partial' => 'yellow',
            'pending' => 'gray',
            default => 'gray'
        };
    }

    public function receiveQuantity(float $quantity): bool
    {
        if ($quantity <= 0 || $this->quantity_received + $quantity > $this->quantity_ordered) {
            return false;
        }

        return $this->update(['quantity_received' => $this->quantity_received + $quantity]);
    }

    public function getTotalReceivedValueAttribute(): float
    {
        return round($this->quantity_received * $this->unit_price, 2);
    }

    public function getTotalPendingValueAttribute(): float
    {
        return round($this->quantity_pending * $this->unit_price, 2);
    }
}
