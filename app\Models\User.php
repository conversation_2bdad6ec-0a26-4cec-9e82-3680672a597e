<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'branch_id',
        'staff_role_id',
        'employee_id',
        'phone',
        'date_of_birth',
        'hire_date',
        'hourly_rate',
        'employment_status',
        'permissions',
        'is_staff',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'date_of_birth' => 'date',
            'hire_date' => 'date',
            'hourly_rate' => 'decimal:2',
            'permissions' => 'array',
            'is_staff' => 'boolean',
        ];
    }

    // Relationships
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    public function staffRole(): BelongsTo
    {
        return $this->belongsTo(StaffRole::class);
    }

    public function orders(): HasMany
    {
        return $this->hasMany(Order::class, 'waiter_id');
    }

    public function shifts(): HasMany
    {
        return $this->hasMany(StaffShift::class);
    }

    public function createdPurchaseOrders(): HasMany
    {
        return $this->hasMany(PurchaseOrder::class, 'created_by');
    }

    public function inventoryTransactions(): HasMany
    {
        return $this->hasMany(InventoryTransaction::class, 'created_by');
    }

    // Scopes
    public function scopeStaff($query)
    {
        return $query->where('is_staff', true);
    }

    public function scopeActive($query)
    {
        return $query->where('employment_status', 'active');
    }

    public function scopeByRole($query, string $role)
    {
        return $query->whereHas('staffRole', function ($q) use ($role) {
            $q->where('name', $role);
        });
    }

    // Methods
    public function hasPermission(string $permission): bool
    {
        // Check individual permissions
        if (in_array($permission, $this->permissions ?? [])) {
            return true;
        }

        // Check role permissions
        if ($this->staffRole && $this->staffRole->hasPermission($permission)) {
            return true;
        }

        return false;
    }

    public function getAllPermissions(): array
    {
        $permissions = $this->permissions ?? [];

        if ($this->staffRole) {
            $permissions = array_merge($permissions, $this->staffRole->permissions ?? []);
        }

        return array_unique($permissions);
    }

    public function isManager(): bool
    {
        return $this->staffRole && strtolower($this->staffRole->name) === 'manager';
    }

    public function isWaiter(): bool
    {
        return $this->staffRole && strtolower($this->staffRole->name) === 'waiter';
    }

    public function isChef(): bool
    {
        return $this->staffRole && strtolower($this->staffRole->name) === 'chef';
    }

    public function isCashier(): bool
    {
        return $this->staffRole && strtolower($this->staffRole->name) === 'cashier';
    }

    public function getDisplayNameAttribute(): string
    {
        return $this->name . ($this->employee_id ? " (#{$this->employee_id})" : '');
    }

    public function getTotalHoursThisWeekAttribute(): float
    {
        return $this->shifts()
            ->thisWeek()
            ->completed()
            ->get()
            ->sum('actual_hours');
    }

    public function getTotalEarningsThisWeekAttribute(): float
    {
        return $this->shifts()
            ->thisWeek()
            ->completed()
            ->get()
            ->sum('earnings');
    }
}
