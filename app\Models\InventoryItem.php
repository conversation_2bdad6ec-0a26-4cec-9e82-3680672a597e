<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class InventoryItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'branch_id',
        'name',
        'sku',
        'description',
        'unit',
        'current_stock',
        'minimum_stock',
        'maximum_stock',
        'unit_cost',
        'category',
        'expiry_date',
        'is_active',
    ];

    protected $casts = [
        'current_stock' => 'decimal:3',
        'minimum_stock' => 'decimal:3',
        'maximum_stock' => 'decimal:3',
        'unit_cost' => 'decimal:2',
        'expiry_date' => 'date',
        'is_active' => 'boolean',
    ];

    // Relationships
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    public function menuItems(): BelongsToMany
    {
        return $this->belongsToMany(MenuItem::class, 'menu_item_ingredients')
            ->withPivot('quantity_required')
            ->withTimestamps();
    }

    public function transactions(): HasMany
    {
        return $this->hasMany(InventoryTransaction::class);
    }

    public function purchaseOrderItems(): HasMany
    {
        return $this->hasMany(PurchaseOrderItem::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeLowStock($query)
    {
        return $query->whereColumn('current_stock', '<=', 'minimum_stock');
    }

    public function scopeOutOfStock($query)
    {
        return $query->where('current_stock', '<=', 0);
    }

    public function scopeExpiringSoon($query, int $days = 7)
    {
        return $query->where('expiry_date', '<=', now()->addDays($days))
                    ->where('expiry_date', '>=', now());
    }

    public function scopeExpired($query)
    {
        return $query->where('expiry_date', '<', now());
    }

    public function scopeByCategory($query, string $category)
    {
        return $query->where('category', $category);
    }

    // Methods
    public function isLowStock(): bool
    {
        return $this->current_stock <= $this->minimum_stock;
    }

    public function isOutOfStock(): bool
    {
        return $this->current_stock <= 0;
    }

    public function isExpiringSoon(int $days = 7): bool
    {
        return $this->expiry_date && 
               $this->expiry_date <= now()->addDays($days) && 
               $this->expiry_date >= now();
    }

    public function isExpired(): bool
    {
        return $this->expiry_date && $this->expiry_date < now();
    }

    public function addStock(float $quantity, float $unitCost = null, string $referenceType = null, int $referenceId = null, string $notes = null): InventoryTransaction
    {
        $this->increment('current_stock', $quantity);
        
        if ($unitCost !== null) {
            $this->update(['unit_cost' => $unitCost]);
        }

        return $this->transactions()->create([
            'type' => 'in',
            'quantity' => $quantity,
            'unit_cost' => $unitCost ?? $this->unit_cost,
            'reference_type' => $referenceType,
            'reference_id' => $referenceId,
            'notes' => $notes ?? "Stock added",
            'created_by' => auth()->id() ?? 1,
        ]);
    }

    public function removeStock(float $quantity, string $referenceType = null, int $referenceId = null, string $notes = null): InventoryTransaction
    {
        $this->decrement('current_stock', $quantity);

        return $this->transactions()->create([
            'type' => 'out',
            'quantity' => -$quantity,
            'unit_cost' => $this->unit_cost,
            'reference_type' => $referenceType,
            'reference_id' => $referenceId,
            'notes' => $notes ?? "Stock removed",
            'created_by' => auth()->id() ?? 1,
        ]);
    }

    public function adjustStock(float $newQuantity, string $reason = null): InventoryTransaction
    {
        $difference = $newQuantity - $this->current_stock;
        $this->update(['current_stock' => $newQuantity]);

        return $this->transactions()->create([
            'type' => 'adjustment',
            'quantity' => $difference,
            'unit_cost' => $this->unit_cost,
            'notes' => $reason ?? "Stock adjustment",
            'created_by' => auth()->id() ?? 1,
        ]);
    }

    public function getStockValueAttribute(): float
    {
        return round($this->current_stock * $this->unit_cost, 2);
    }

    public function getStockStatusAttribute(): string
    {
        if ($this->isOutOfStock()) {
            return 'out_of_stock';
        } elseif ($this->isLowStock()) {
            return 'low_stock';
        } else {
            return 'in_stock';
        }
    }

    public function getStockStatusColorAttribute(): string
    {
        return match($this->stock_status) {
            'out_of_stock' => 'red',
            'low_stock' => 'yellow',
            'in_stock' => 'green',
            default => 'gray'
        };
    }

    public function getReorderQuantityAttribute(): float
    {
        if ($this->maximum_stock) {
            return $this->maximum_stock - $this->current_stock;
        }
        
        return $this->minimum_stock * 2; // Default to 2x minimum stock
    }
}
