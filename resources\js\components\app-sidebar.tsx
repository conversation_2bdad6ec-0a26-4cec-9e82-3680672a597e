import { NavFooter } from '@/components/nav-footer';
import { NavMain } from '@/components/nav-main';
import { NavUser } from '@/components/nav-user';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { type NavItem } from '@/types';
import { Link } from '@inertiajs/react';
import {
    BookOpen,
    Folder,
    LayoutGrid,
    Table,
    Menu,
    ShoppingCart,
    Users,
    Calendar,
    Package,
    UserCheck,
    BarChart3,
    Settings,
    ChefHat,
    CreditCard,
    Gift
} from 'lucide-react';
import AppLogo from './app-logo';

const mainNavItems: NavItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
        icon: LayoutGrid,
    },
    {
        title: 'Orders',
        href: '/orders',
        icon: ShoppingCart,
        items: [
            {
                title: 'All Orders',
                href: '/orders',
            },
            {
                title: 'POS System',
                href: '/orders-pos',
            },
            {
                title: 'Kitchen Display',
                href: '/orders-kitchen',
            },
        ],
    },
    {
        title: 'Tables',
        href: '/tables',
        icon: Table,
        items: [
            {
                title: 'Manage Tables',
                href: '/tables',
            },
            {
                title: 'Floor Plan',
                href: '/tables-floor-plan',
            },
        ],
    },
    {
        title: 'Menu',
        href: '/menu',
        icon: Menu,
        items: [
            {
                title: 'View Menu',
                href: '/menu',
            },
            {
                title: 'Categories',
                href: '/menu/categories',
            },
            {
                title: 'Menu Items',
                href: '/menu/items',
            },
        ],
    },
    {
        title: 'Customers',
        href: '/customers',
        icon: Users,
    },
    {
        title: 'Reservations',
        href: '/reservations',
        icon: Calendar,
        items: [
            {
                title: 'All Reservations',
                href: '/reservations',
            },
            {
                title: 'Calendar View',
                href: '/reservations-calendar',
            },
        ],
    },
    {
        title: 'Inventory',
        href: '/inventory',
        icon: Package,
        items: [
            {
                title: 'Stock Items',
                href: '/inventory',
            },
            {
                title: 'Suppliers',
                href: '/suppliers',
            },
            {
                title: 'Purchase Orders',
                href: '/purchase-orders',
            },
        ],
    },
    {
        title: 'Staff',
        href: '/staff',
        icon: UserCheck,
        items: [
            {
                title: 'Employees',
                href: '/staff',
            },
            {
                title: 'Roles',
                href: '/staff/roles',
            },
            {
                title: 'Schedules',
                href: '/staff/schedules',
            },
        ],
    },
    {
        title: 'Reports',
        href: '/reports',
        icon: BarChart3,
        items: [
            {
                title: 'Sales Report',
                href: '/reports/sales',
            },
            {
                title: 'Tax Report',
                href: '/reports/tax',
            },
            {
                title: 'Inventory Report',
                href: '/reports/inventory',
            },
        ],
    },
];

const footerNavItems: NavItem[] = [
    {
        title: 'Settings',
        href: '/settings',
        icon: Settings,
    },
    {
        title: 'Help & Support',
        href: '/help',
        icon: BookOpen,
    },
];

export function AppSidebar() {
    return (
        <Sidebar collapsible="icon" variant="inset">
            <SidebarHeader>
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton size="lg" asChild>
                            <Link href="/dashboard" prefetch>
                                <AppLogo />
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarHeader>

            <SidebarContent>
                <NavMain items={mainNavItems} />
            </SidebarContent>

            <SidebarFooter>
                <NavFooter items={footerNavItems} className="mt-auto" />
                <NavUser />
            </SidebarFooter>
        </Sidebar>
    );
}
