<?php

namespace App\Http\Controllers;

use App\Models\Branch;
use App\Models\Customer;
use App\Models\InventoryItem;
use App\Models\Order;
use App\Models\Reservation;
use App\Models\Table;
use Illuminate\Http\Request;
use Inertia\Inertia;

class DashboardController extends Controller
{
    public function index(Request $request)
    {
        $user = $request->user();
        $branchId = $user->branch_id ?? Branch::first()?->id;

        // Get today's statistics
        $todayStats = $this->getTodayStats($branchId);
        
        // Get recent orders
        $recentOrders = Order::with(['table', 'customer', 'waiter'])
            ->where('branch_id', $branchId)
            ->latest()
            ->take(10)
            ->get();

        // Get today's reservations
        $todayReservations = Reservation::with(['table', 'customer'])
            ->where('branch_id', $branchId)
            ->today()
            ->orderBy('reservation_datetime')
            ->get();

        // Get table status
        $tableStatus = Table::where('branch_id', $branchId)
            ->selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();

        // Get low stock items
        $lowStockItems = InventoryItem::where('branch_id', $branchId)
            ->lowStock()
            ->active()
            ->take(5)
            ->get();

        // Get sales chart data (last 7 days)
        $salesChartData = $this->getSalesChartData($branchId);

        return Inertia::render('Dashboard', [
            'stats' => $todayStats,
            'recentOrders' => $recentOrders,
            'todayReservations' => $todayReservations,
            'tableStatus' => $tableStatus,
            'lowStockItems' => $lowStockItems,
            'salesChartData' => $salesChartData,
        ]);
    }

    private function getTodayStats($branchId)
    {
        $today = now()->startOfDay();
        $yesterday = now()->subDay()->startOfDay();

        // Today's orders
        $todayOrders = Order::where('branch_id', $branchId)
            ->whereDate('created_at', $today)
            ->count();

        $yesterdayOrders = Order::where('branch_id', $branchId)
            ->whereDate('created_at', $yesterday)
            ->count();

        // Today's revenue
        $todayRevenue = Order::where('branch_id', $branchId)
            ->whereDate('created_at', $today)
            ->where('status', 'completed')
            ->sum('total_amount');

        $yesterdayRevenue = Order::where('branch_id', $branchId)
            ->whereDate('created_at', $yesterday)
            ->where('status', 'completed')
            ->sum('total_amount');

        // Today's customers
        $todayCustomers = Order::where('branch_id', $branchId)
            ->whereDate('created_at', $today)
            ->distinct('customer_id')
            ->count('customer_id');

        $yesterdayCustomers = Order::where('branch_id', $branchId)
            ->whereDate('created_at', $yesterday)
            ->distinct('customer_id')
            ->count('customer_id');

        // Active tables
        $activeTables = Table::where('branch_id', $branchId)
            ->whereIn('status', ['occupied', 'reserved'])
            ->count();

        $totalTables = Table::where('branch_id', $branchId)
            ->where('is_active', true)
            ->count();

        return [
            'orders' => [
                'today' => $todayOrders,
                'change' => $yesterdayOrders > 0 ? (($todayOrders - $yesterdayOrders) / $yesterdayOrders) * 100 : 0,
            ],
            'revenue' => [
                'today' => $todayRevenue,
                'change' => $yesterdayRevenue > 0 ? (($todayRevenue - $yesterdayRevenue) / $yesterdayRevenue) * 100 : 0,
            ],
            'customers' => [
                'today' => $todayCustomers,
                'change' => $yesterdayCustomers > 0 ? (($todayCustomers - $yesterdayCustomers) / $yesterdayCustomers) * 100 : 0,
            ],
            'tables' => [
                'active' => $activeTables,
                'total' => $totalTables,
                'percentage' => $totalTables > 0 ? ($activeTables / $totalTables) * 100 : 0,
            ],
        ];
    }

    private function getSalesChartData($branchId)
    {
        $data = [];
        
        for ($i = 6; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $revenue = Order::where('branch_id', $branchId)
                ->whereDate('created_at', $date)
                ->where('status', 'completed')
                ->sum('total_amount');

            $data[] = [
                'date' => $date->format('M d'),
                'revenue' => (float) $revenue,
            ];
        }

        return $data;
    }
}
