<?php

use App\Http\Controllers\CustomerController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\MenuController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\ReservationController;
use App\Http\Controllers\TableController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');

// Public menu route
Route::get('/menu', [MenuController::class, 'publicMenu'])->name('public.menu');

Route::middleware(['auth', 'verified'])->group(function () {
    // Dashboard
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Tables Management
    Route::resource('tables', TableController::class);
    Route::get('/tables-floor-plan', [TableController::class, 'floorPlan'])->name('tables.floor-plan');
    Route::patch('/tables/{table}/status', [TableController::class, 'updateStatus'])->name('tables.update-status');
    Route::patch('/tables/{table}/position', [TableController::class, 'updatePosition'])->name('tables.update-position');

    // Menu Management
    Route::get('/menu', [MenuController::class, 'index'])->name('menu.index');
    Route::get('/menu/categories', [MenuController::class, 'categories'])->name('menu.categories');
    Route::post('/menu/categories', [MenuController::class, 'storeCategory'])->name('menu.categories.store');
    Route::patch('/menu/categories/{category}', [MenuController::class, 'updateCategory'])->name('menu.categories.update');
    Route::delete('/menu/categories/{category}', [MenuController::class, 'destroyCategory'])->name('menu.categories.destroy');

    Route::get('/menu/items', [MenuController::class, 'items'])->name('menu.items');
    Route::post('/menu/items', [MenuController::class, 'storeItem'])->name('menu.items.store');
    Route::patch('/menu/items/{item}', [MenuController::class, 'updateItem'])->name('menu.items.update');
    Route::patch('/menu/items/{item}/availability', [MenuController::class, 'toggleAvailability'])->name('menu.items.toggle-availability');
    Route::delete('/menu/items/{item}', [MenuController::class, 'destroyItem'])->name('menu.items.destroy');

    // Orders Management
    Route::resource('orders', OrderController::class);
    Route::get('/orders-kitchen', [OrderController::class, 'kitchen'])->name('orders.kitchen');
    Route::get('/orders-pos', [OrderController::class, 'pos'])->name('orders.pos');
    Route::patch('/orders/{order}/status', [OrderController::class, 'updateStatus'])->name('orders.update-status');
    Route::patch('/order-items/{orderItem}/status', [OrderController::class, 'updateItemStatus'])->name('order-items.update-status');
    Route::post('/orders/{order}/items', [OrderController::class, 'addItem'])->name('orders.add-item');
    Route::delete('/order-items/{orderItem}', [OrderController::class, 'removeItem'])->name('order-items.remove');

    // Customers Management
    Route::resource('customers', CustomerController::class);
    Route::get('/customers/search', [CustomerController::class, 'search'])->name('customers.search');
    Route::get('/customers/{customer}/loyalty-history', [CustomerController::class, 'loyaltyHistory'])->name('customers.loyalty-history');
    Route::post('/customers/{customer}/loyalty-points/add', [CustomerController::class, 'addLoyaltyPoints'])->name('customers.add-loyalty-points');
    Route::post('/customers/{customer}/loyalty-points/redeem', [CustomerController::class, 'redeemLoyaltyPoints'])->name('customers.redeem-loyalty-points');

    // Reservations Management
    Route::resource('reservations', ReservationController::class);
    Route::get('/reservations-calendar', [ReservationController::class, 'calendar'])->name('reservations.calendar');
    Route::patch('/reservations/{reservation}/status', [ReservationController::class, 'updateStatus'])->name('reservations.update-status');
    Route::post('/reservations/{reservation}/cancel', [ReservationController::class, 'cancel'])->name('reservations.cancel');
    Route::post('/reservations/check-availability', [ReservationController::class, 'checkAvailability'])->name('reservations.check-availability');
});

require __DIR__ . '/settings.php';
require __DIR__ . '/auth.php';
