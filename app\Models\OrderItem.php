<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class OrderItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'menu_item_id',
        'quantity',
        'unit_price',
        'total_price',
        'customizations',
        'special_instructions',
        'status',
        'prepared_at',
    ];

    protected $casts = [
        'quantity' => 'integer',
        'unit_price' => 'decimal:2',
        'total_price' => 'decimal:2',
        'customizations' => 'array',
        'prepared_at' => 'datetime',
    ];

    // Relationships
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    public function menuItem(): BelongsTo
    {
        return $this->belongsTo(MenuItem::class);
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopePreparing($query)
    {
        return $query->where('status', 'preparing');
    }

    public function scopeReady($query)
    {
        return $query->where('status', 'ready');
    }

    public function scopeServed($query)
    {
        return $query->where('status', 'served');
    }

    // Methods
    public function updateStatus(string $status): bool
    {
        $validStatuses = ['pending', 'preparing', 'ready', 'served'];
        
        if (!in_array($status, $validStatuses)) {
            return false;
        }

        $data = ['status' => $status];
        
        if ($status === 'ready') {
            $data['prepared_at'] = now();
        }

        return $this->update($data);
    }

    public function startPreparing(): bool
    {
        if ($this->status !== 'pending') {
            return false;
        }

        // Reduce inventory when starting preparation
        $this->menuItem->reduceInventory($this->quantity);

        return $this->updateStatus('preparing');
    }

    public function markAsReady(): bool
    {
        if ($this->status !== 'preparing') {
            return false;
        }

        return $this->updateStatus('ready');
    }

    public function markAsServed(): bool
    {
        if ($this->status !== 'ready') {
            return false;
        }

        return $this->updateStatus('served');
    }

    public function getTotalWithCustomizationsAttribute(): float
    {
        $total = $this->total_price;
        
        if ($this->customizations) {
            foreach ($this->customizations as $customization) {
                if (isset($customization['price'])) {
                    $total += $customization['price'] * $this->quantity;
                }
            }
        }

        return round($total, 2);
    }

    public function getDisplayNameAttribute(): string
    {
        $name = $this->menuItem->name;
        
        if ($this->customizations) {
            $customizationNames = array_column($this->customizations, 'name');
            if (!empty($customizationNames)) {
                $name .= ' (' . implode(', ', $customizationNames) . ')';
            }
        }

        return $name;
    }
}
